import { DataTypes } from "sequelize";
import { sequalize } from "../DB/sequlize.js";
import { record, string } from "zod";
import { createHash } from "node:crypto";
import mongoose, { model } from "mongoose";
import { access } from "node:fs";
import { AcessLevel } from "./AccessLevel.js";
import { type } from "node:os";

export const User = sequalize.define(
  "users",
  {
    id: { primaryKey: true, autoIncrement: true, type: DataTypes.INTEGER },
    name: DataTypes.STRING,
    type: DataTypes.STRING,
    status: DataTypes.ENUM("ACTIVE", "SUSPENDED"),
    username: { type: DataTypes.STRING, unique: true },
    password: DataTypes.STRING,
  },
  {
    hooks: {
      beforeCreate: (record, options) => {
        console.log(
          "hash-",
          createHash("sha256").update(record.password).digest("hex")
        );
        record.dataValues.password = createHash("sha256")
          .update(record.password)
          .digest("hex");
      },
      beforeUpdate: (record, options) => {
        if (record.changed("password")) {
          console.log("updating password hash");
          record.dataValues.password = createHash("sha256")
            .update(record.password)
            .digest("hex");
        }
      },
    },
  }
);

/* const UserSchema = new mongoose.Schema({
  name: { type: String, required: true },
  userName: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  accessLevels: { type: [String], required: true },
}); */

/* export const User = model("User", UserSchema);
 */
