.flip-container {
    perspective: 1000px; /* Enable 3D space for the flipping effect */
    width: 100%; /* Set width */
    height: 50px; /* Set height */
  }
  
  .flip-card {
    position: relative;
    width: 100%;
    height: 100%;
    transform-style: preserve-3d; /* Preserve 3D transforms */
    transition: transform 0.6s; /* Smooth flipping */
  }
  
  .flip-card .front, .flip-card .back {
    position: absolute;
    width: 100%;
    height: 100%;
    backface-visibility: hidden; /* Hide the back side when not visible */
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .flip-card .back {
    transform: rotateY(180deg); /* Back is flipped by 180 degrees initially */
  }
  
  .flip-container.flipped .flip-card {
    transform: rotateY(180deg); /* Flip the card */
  }
  