import moment from "moment";
import { Attendence } from "../models/index.js";
import xlsx, { write } from "xlsx";
import path from "path";
import { __dirname } from "../server.js";
import { promisePool } from "../DB/Database.js";
import { z } from "zod";
import { getTraineeByATT_NO } from "./TraineeController.js";
import { updatePaymentTable } from "./PaymentController.js";
import { BSON } from "bson";
import pLimit from "p-limit";
const { readFile, utils } = xlsx;

const startTime = moment("8:30", "HH:mm");
const midTime = moment("12:30", "HH:mm");
const endTime = moment("16:30", "HH:mm");

export const index = (req, res, next) => {
  return res.status(200).json(req);
};

/**
 * responsibilities ->  
-analyzing the attendence sheet recieved
-sending the attendence sheet buffer to the user as a excel file
-update the database attendences
 * @param {*} req
 * @param {*} res
 */
export const AnalyzeAttendenceSheet = async (req, res, next) => {
  const schema = z.object({
    month: z.coerce.number().gt(0).lt(13),
    year: z.coerce.number().gt(2000),
  });

  try {
    const requestData = schema.parse(req.body);
    res.setHeader("Content-Disposition", "attachment; filename=attendance.xlsx");
    res.setHeader(
      "Content-Type",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    );
    //console.log("year - " + requestData.year, "month -" + requestData.month);
    const filePath = path.join(__dirname, "../uploads/", req.file.filename); // file path to save the incoming file temporily
    const workbook = readFile(filePath); //reading the excel file
    const workSheet = workbook.Sheets[workbook.SheetNames[0]]; //accessing the sheet of the excel file
    const rows = utils.sheet_to_json(workSheet); //rows of the excel file as json objects
    const newMap = constructEmployeeDataMap(rows); //construct map from the data recieved from the excel file
    console.log(rows);
    const workingDays = await getWorkingDays(requestData.month, requestData.year);
    await deleteAttendeceRecords(requestData.year, requestData.month);
    await insertAttendencesToDatabase(workingDays, newMap);
    const buffer = await createAttendeceSheet(newMap, workingDays); //construct the excel file buffer
    await updatePaymentTable(schema.parse(req.body));
    return res.status(200).send(buffer);
  } catch (error) {
    next(error);
  }
};

/**
 *responsible for anlaysing the rows of the excel file and construct a Map ATT_NO -> DATES MAP -> DATE -> {on:time,off:time}
 * @param {Array[Array<Object>]} rows rows of the incomming excel sheet
 * @returns {Map<number,Map<string,Object>>} the map which has the all the attendence records
 */
const constructEmployeeDataMap = (rows) => {
  try {
    let employees = new Map();
    let rowsCounter = 0;
    let idsCounter = 0;
    rows.forEach((row) => {
      const id = row.id;
      const date = moment(parseExcelDate(row.date), "MM/DD/YYYY").clone().format("YYYY-MM-DD");
      const time = moment(parseFractionalTime(row.time), "HH:mm:ss").clone();
      rowsCounter++;
      //looping through the rows
      if (employees.has(id)) {
        const employee = employees.get(id); //retrieve the employee attendeces map
        //check weather there is a attendece record for the date
        if (employee.has(date)) {
          const attendence = employee.get(date);
          if (time.isBefore(midTime)) {
            //check if on record already exists
            if (attendence.on) {
              //check if existing time is earlier then the new on time
              if (time.isBefore(attendence.on)) {
                attendence.on = time; //as we need the earliest time
              }
            } else {
              attendence.on = time;
            }
          } else {
            //check if on record already exists
            if (attendence.off) {
              //check if existing time is earlier then the new off time
              if (time.isBefore(attendence.off)) {
                attendence.off = time;
              }
            } else {
              attendence.off = time;
            }
          }
        } else {
          if (time.isBefore(midTime)) {
            employee.set(date, { on: time });
          } else {
            employee.set(date, { off: time });
          }
        }
      } else {
        if (/^[9|8][\d]{6}$/.test(id)) {
          idsCounter++;
          let dates = new Map();
          if (time.isBefore(midTime)) {
            dates.set(date, { on: time });
          } else {
            dates.set(date, { off: time });
          }
          employees.set(id, dates);
        }
      }
    });
    return employees;
  } catch (error) {
    console.log("Failded to create Data map");
    throw error;
  }
};

/**
 *delete attendence records related to month and year
 * @param {number} year
 * @param {number} month
 */
const deleteAttendeceRecords = async (year, month) => {
  try {
    const deleteResults = await promisePool.query(
      "DELETE from attendences where year(date)=? and month(date)=? ",
      [year, month]
    );
  } catch (error) {
    console.log("failed to delete attendence");
    throw error;
  }
};

/**
 *responsible analyze the dataMap for inserting the attendence records to the datbase
 * @param {*} workingDays array of the working Days created by getWorkingDays()
 * @param {*} dataMap Map of exployees data
 */
const insertAttendencesToDatabase = async (workingDays, dataMap) => {
  try {
    const limit = pLimit(15); // max 15 concurrent promises
    const allTasks = [];

    for (const [ATT_NO, dates] of dataMap.entries()) {
      //get the trainee related to the attendence
      const trainee = await getTraineeByATT_NO(ATT_NO);
      if (trainee) {
        for (const workingDay of workingDays) {
          const task = limit(async () => {
            const attendanceRecord = dates.get(workingDay); //get the attendence record of the working day
            if (attendanceRecord) {
              //if there is a attendece then on time and off time must be recorded
              //if there is a attendence record for the day then check if the attendence is acceptable if acceptable then 1 else 0
              const attendance = Attendence.build({
                trainee_id: trainee.id,
                date: workingDay,
              });

              if (attendanceRecord.on) {
                attendance.on_time = attendanceRecord.on.format("HH:mm:ss");
              }

              if (attendanceRecord.off) {
                attendance.off_time = attendanceRecord.off.format("HH:mm:ss");
              }
              attendance.status = checkAttendenceStatus(attendanceRecord);
              await attendance.save();
            } else {
              //if no attendence record for the following working day then absent means 0 to attendece then no on time and off time
              const attendence = Attendence.create({
                trainee_id: trainee.id,
                date: workingDay,
                status: false,
              });
            }
          });
          allTasks.push(task);
        }
      }
    }
    await Promise.all(allTasks);
  } catch (error) {
    console.log("failed to insert Attendences to the Database");
    throw error;
  }
};
/**
 *
 * @param {Object} attendanceRecord attendece object recieved from the employee data map which has the moment
 insatnces fo the ontime and off time
 * @returns {Boolean} status of the attendece record recieved wheather present or absent represented by a boolean
 */
const checkAttendenceStatus = (attendanceRecord) => {
  try {
    if (attendanceRecord.on && attendanceRecord.off) {
      if (attendanceRecord.on.isBefore(startTime) && attendanceRecord.off.isAfter(endTime)) {
        return true;
      } else {
        return false;
      }
    } else {
      return false;
    }
  } catch (error) {
    console.log("failed to check Attendece Status");
    throw error;
  }
};

/**
 *get the working days excluding all holidays as array of string
 *Date is represented in YYYY-MM-DD format
 * @param {*} month
 * @param {*} year
 * @returns {Promise<Array<string>} array of dates
 */
const getWorkingDays = async (month, year) => {
  try {
    const holidays = await getHolidaysArray(month, year); //get the holidays as a array of holiday dates
    const startOfTheMonth = moment([year, month - 1]);
    const endOFTheMonth = startOfTheMonth.clone().endOf("month");

    let currentDay = startOfTheMonth;
    let holidaysCount = 0;
    let workDaysCount = 0;
    const workDays = []; //initialize the working days array

    //looping through the month of the year
    while (currentDay.isSameOrBefore(endOFTheMonth)) {
      // Check if it's a weekday (Monday to Friday)
      if (currentDay.isoWeekday() <= 5) {
        // Check if the current day is a holiday
        if (holidays.some((holiday) => holiday.isSame(currentDay, "day"))) {
          holidaysCount++;
        } else {
          workDaysCount++;
          workDays.push(currentDay.format("YYYY-MM-DD"));
        }
      }
      currentDay.add(1, "day"); // Move to the next day after processing (whether it's a holiday or not)
    }
    //console.log("holidays count- " + holidaysCount);
    //console.log("workDays count - " + workDaysCount);
    return workDays;
  } catch (error) {
    console.log("Failed to get Working Days Array");
    throw error;
  }
};

/**
 * retrives the holidays of the specific year and month from the database
 * converting the event format of the database to array of single days
 * @param {number} month
 * @param {number} year
 * @returns {Promise<Array<moment.Moment>>} array of moment objects of the holidays
 */
const getHolidaysArray = async (month, year) => {
  try {
    let holidays = [];
    /* 
    all the holidays in the respective year and month
    ["2024-12-01"]
  */
    const [holidayEvents] = await promisePool.query(
      `SELECT 
    start_date,end_date- interval 1 day as end_date,description 
    FROM holidays 
    where (year(start_date)=? or year(end_date)=?)
    and (month(start_date)=? or month(end_date)=?);`,
      [year, year, month, month]
    );

    holidayEvents.forEach((event) => {
      const start = moment(event.start_date).clone();
      const end = moment(event.end_date).clone();
      let currentDay = start;

      //looping through each day of the event
      while (currentDay.isSameOrBefore(end)) {
        if (currentDay.year() == year && currentDay.month() + 1 == month) {
          holidays.push(currentDay.clone());
        }
        currentDay.add(1, "day");
      }
    });
    return holidays;
  } catch (error) {
    console.log("Failed to get Holidays Array");
    throw error;
  }
};

/* const timeConverter = (time) => {
  const totalSeconds = time * 86400;
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = Math.round(totalSeconds % 60);
  const formattedTime = `${hours.toString().padStart(2, "0")}:${minutes
    .toString()
    .padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
  return formattedTime;
}; */

const dateConverter = (date) => {
  const jsDate = new Date((date - 25569) * 86400 * 1000);
  return jsDate.toLocaleDateString();
};

/* const dateConverterInverse = (date) => {
  const jsDate = new Date((date - 25569) * 86400 * 1000);
  return jsDate.toLocaleDateString();
}; */
const parseExcelDate = (date) => {
  const jsDate = new Date((date - 25569) * 86400 * 1000);
  return jsDate.toLocaleDateString();
};

const parseFractionalTime = (time) => {
  const totalSeconds = Math.floor(time * 86400); // total seconds in a day
  const hours = String(Math.floor(totalSeconds / 3600)).padStart(2, "0");
  const minutes = String(Math.floor((totalSeconds % 3600) / 60)).padStart(2, "0");
  const seconds = String(totalSeconds % 60).padStart(2, "0");
  return `${hours}:${minutes}:${seconds}`;
};

// Helper function to convert fractional time to a readable time string
const fractionalToTime = (fraction) => {
  const totalSeconds = Math.floor(fraction * 86400); // total seconds in a day
  const hours = String(Math.floor(totalSeconds / 3600)).padStart(2, "0");
  const minutes = String(Math.floor((totalSeconds % 3600) / 60)).padStart(2, "0");
  const seconds = String(totalSeconds % 60).padStart(2, "0");
  return `${hours}:${minutes}:${seconds}`;
};

export const getAttendenceOfTrainee = async (req, res, next) => {
  const schema = z.object({
    traineeId: z.coerce.number(),
  });
  try {
    const data = schema.parse(req.params);
    const [attendences] = await promisePool.query(
      `
      select 
		    year(attendences.date) as year,
		    month(attendences.date) as month,
		    date(attendences.date) as date,
		    attendences.status,
		    journey.start_date,
		    journey.end_date
      from 
		    attendences,journey where
        attendences.trainee_id=journey.trainee_id and 
        attendences.trainee_id =? and
        date between start_date and end_date;
      `,
      [data.traineeId]
    );
    /* 
    {
      year : 2024,
      months:[
        {
          month : 1,
          percentage:90%
        }
      ]
    }
    */
    const summary = [];
    let totalCount = 0;
    let totalPresentCount = 0;
    for (const attendence of attendences) {
      totalCount++;
      if (attendence.status == 1) {
        totalPresentCount++;
      }
      const yearRecord = summary.find((record) => record.year == attendence.year);
      if (yearRecord) {
        //year object is initialized
        const monthRecord = yearRecord.months.find((record) => record.month == attendence.month);
        if (monthRecord) {
          //old record for
          monthRecord.totalCount++;
          if (attendence.status == 1) {
            monthRecord.presentCount++;
          }
        } else {
          //new record for the month
          yearRecord.months.push({
            month: attendence.month,
            presentCount: attendence.status,
            totalCount: 1,
          });
        }
      } else {
        //year object is not intiialized
        totalPresentCount;
        summary.push({
          year: attendence.year,
          months: [
            {
              month: attendence.month,
              presentCount: attendence.status,
              totalCount: 1,
            },
          ],
        });
      }
    }
    return res.status(200).json({ totalPresentCount, totalCount, summary });
  } catch (error) {
    next(error);
  }
};

/* function convertMapToReadableFormat(dataMap) {
  // Process the main map
  const result = {};

  for (const [outerKey, innerMap] of dataMap.entries()) {
    result[outerKey] = {};
    for (const [dateSerial, times] of innerMap.entries()) {
      const dateStr = dateConverter(dateSerial); // Convert serial date to readable date
      result[outerKey][dateStr] = {};
      for (const [timeKey, timeValue] of Object.entries(times)) {
        result[outerKey][dateStr][timeKey] = fractionalToTime(timeValue); // Convert fractional time to readable time
      }
    }
  }

  return result;
} */

/**
 *responsible for analysing the data map and and creating the attendence sheet file
 * @param {*} employeesMap map of the employee data
 * @param {*} workingDays array of the working Days created by getWorkingDays()
 * @returns the buffer of the excel file
 */
const createAttendeceSheet = async (employeesMap, workingDays) => {
  try {
    const headers = [
      "S/NO",
      "ATTN NO",
      "REG NO",
      "END DATE",
      "NAME",
      ...workingDays,
      "ATTN TOTAL",
      "PAY AMOUNT(Rs)",
    ]; /* [id,date1,date2,date3] */
    let rows = [headers];
    let sNo = 0;
    for (const [ATT_NO, dates] of employeesMap.entries()) {
      //dates have the attended dates and on off times
      //id in the sheet is the attendence number
      //finding the trainee of the respective attendence number and extract the reg no
      /* if (ATT_NO == 9240351) {
        console.log("dates", dates);
      } */
      if (/^[9][\d]{6}$/.test(ATT_NO)) {
        sNo++;
        let row = [sNo, ATT_NO];
        const trainee = await getTraineeByATT_NO(ATT_NO);
        let TraineeEndDate;
        if (trainee) {
          if (trainee.REG_NO) {
            row.push(trainee.REG_NO);
          } else {
            row.push("NO RECORD");
          }
          if (trainee.end_date) {
            row.push(trainee.end_date);
            TraineeEndDate = moment(trainee.end_date);
          } else {
            row.push("NO RECORD");
          }
          if (trainee.name) {
            row.push(trainee.name);
          } else {
            row.push("NO RECORD");
          }
        } else {
          row.push("NO RECORD", "NO RECORD", "NO RECORD");
        }
        /* looping through the days */
        /* if there is entry for the date and fullfills the condition then represent it from 1 else 0 */
        let presentDaysCount = 0;
        workingDays.forEach((workingDay) => {
          //check if there is record for this day
          const attendence = dates.get(workingDay);

          //check if the trainee's end date has exceeded apply 0 for all the working days after end date
          if (attendence) {
            if (ATT_NO == 9240351) {
              console.log(workingDay, attendence, checkAttendenceStatus(attendence));
            }
            /* if (ATT_NO == 9230566) { //use if necessary for debugging
              console.log(ATT_NO, workingDay, attendence);
            } */
            if (checkAttendenceStatus(attendence)) {
              if (TraineeEndDate) {
                const momentWorkingDay = moment(workingDay);
                if (momentWorkingDay.isAfter(TraineeEndDate) && false) {
                  //console.log("exceeded", "workingDay-", momentWorkingDay);
                  row.push(0);
                } else {
                  presentDaysCount++;
                  row.push(1);
                }
                //check if current day is exceeded the trainee end date
              } else {
                presentDaysCount++;
                row.push(1);
              }
            } else {
              row.push(0);
            }
          } else {
            row.push(0);
          }
        });
        row.push(presentDaysCount);
        row.push(presentDaysCount * 500);
        rows.push(row);
      }
    }
    const workbook = xlsx.utils.book_new();
    const sheet = xlsx.utils.aoa_to_sheet(rows); // Convert rows array to sheet
    xlsx.utils.book_append_sheet(workbook, sheet, "attendance");
    const buffer = write(workbook, { type: "buffer", bookType: "xlsx" });
    return buffer;
  } catch (error) {
    console.log("Failed to create Attendence Sheet");
    throw error;
  }

  //
};

const createAttendeceSheetFromDB = async (employeesMap, workingDays) => {
  try {
    const headers = [
      "S/NO",
      "ATTN NO",
      "REG NO",
      "END DATE",
      "NAME",
      ...workingDays,
      "ATTN TOTAL",
      "PAY AMOUNT(Rs)",
    ]; /* [id,date1,date2,date3] */
    let rows = [headers];
    let sNo = 0;
    for (const [ATT_NO, dates] of employeesMap.entries()) {
      //dates have the attended dates and on off times
      //id in the sheet is the attendence number
      //finding the trainee of the respective attendence number and extract the reg no
      sNo++;
      let row = [sNo, ATT_NO];
      const trainee = await getTraineeByATT_NO(ATT_NO);
      let TraineeEndDate;
      if (trainee) {
        if (trainee.REG_NO) {
          row.push(trainee.REG_NO);
        } else {
          row.push("NO RECORD");
        }
        if (trainee.end_date) {
          row.push(trainee.end_date);
          TraineeEndDate = moment(trainee.end_date);
        } else {
          row.push("NO RECORD");
        }
        if (trainee.name) {
          row.push(trainee.name);
        } else {
          row.push("NO RECORD");
        }
      } else {
        row.push("NO RECORD", "NO RECORD", "NO RECORD");
      }
      /* looping through the days */
      /* if there is entry for the date and fullfills the condition then represent it from 1 else 0 */
      let presentDaysCount = 0;
      workingDays.forEach((workingDay) => {
        //check if there is record for this day
        const attendence = dates.get(workingDay);
        //check if the trainee's end date has exceeded apply 0 for all the working days after end date
        if (attendence) {
          /* if (ATT_NO == 9230566) { //use if necessary for debugging
            console.log(ATT_NO, workingDay, attendence);
          } */
          if (checkAttendenceStatus(attendence)) {
            if (TraineeEndDate) {
              const momentWorkingDay = moment(workingDay);
              if (momentWorkingDay.isAfter(TraineeEndDate)) {
                //console.log("exceeded", "workingDay-", momentWorkingDay);
                row.push(0);
              } else {
                presentDaysCount++;
                row.push(1);
              }
              //check if current day is exceeded the trainee end date
            } else {
              presentDaysCount++;
              row.push(1);
            }
          } else {
            row.push(0);
          }
        } else {
          row.push(0);
        }
      });
      row.push(presentDaysCount);
      rows.push(row);
    }

    const workbook = xlsx.utils.book_new();
    const sheet = xlsx.utils.aoa_to_sheet(rows); // Convert rows array to sheet
    xlsx.utils.book_append_sheet(workbook, sheet, "attendance");
    const buffer = write(workbook, { type: "buffer", bookType: "xlsx" });
    return buffer;
  } catch (error) {
    console.log("Failed to create Attendence Sheet");
    throw error;
  }

  //
};

export const getAttendenceRecords = async (req, res, next) => {
  const schema = z
    .object({
      month: z.coerce.number().gt(0).lt(13).optional(),
      year: z.coerce.number().gt(2000).optional(),
      trainee_id: z.coerce.number().optional(),
    })
    .refine(
      (data) => {
        if (data.month || data.year || data.trainee_id) {
          return true;
        } else {
          return false;
        }
      },
      { message: "At least one of the parameters are required" }
    );

  try {
    const data = schema.parse(req.query);
    console.log(req.query);
    let conditions = [];
    if (data.trainee_id) {
      conditions.push(`trainee.id=${data.trainee_id}`);
    }
    if (data.month) {
      conditions.push(`month(attendences.date)=${data.month}`);
    }
    if (data.year) {
      conditions.push(`year(attendences.date)=${data.year}`);
    }
    const trainees = await createAttendeceTraineeArrayFromDB(conditions);

    res.status(200).json(trainees);
    //res.status(200).send(convertToBson(records));
  } catch (error) {
    console.log(error);
    next(error);
  }
};

export const convertToBson = (object) => {
  const serialized = BSON.serialize({ data: object });
  console.log(serialized);
  return serialized;
};

/**
 *creates a array of trainees with their respective attendences
 * @param {*} conditions array of conditions with respect to the fields database name
 */
export const createAttendeceTraineeArrayFromDB = async (conditions) => {
  const trainees = [];

  const query = `
      SELECT
        trainee.id AS trainee_id,
        trainee.ATT_NO,
        trainee.REG_NO,
        attendences.date,
        attendences.on_time,
        attendences.off_time,
        attendences.status,
        journey.start_date,
        journey.end_date,
        trainee.name
      FROM
        attendences,trainee,journey
      where attendences.trainee_id=trainee.id and trainee.id=journey.trainee_id and ${conditions.join(
        " AND "
      )}
    `;

  console.log(query);
  const [records] = await promisePool.query(query);
  //console.log(records);
  for (const record of records) {
    const trainee = trainees.find((trainee) => trainee.trainee_id == record.trainee_id);
    if (trainee) {
      trainee.attendences.push({
        date: moment(record.date).format("YYYY-MM-DD"),
        on_time: record.on_time,
        off_time: record.off_time,
        status: record.status,
      });
    } else {
      trainees.push({
        trainee_id: record.trainee_id,
        REG_NO: record.REG_NO,
        ATT_NO: record.ATT_NO,
        end_date: record.end_date,
        name: record.name,
        attendences: [
          {
            date: moment(record.date).format("YYYY-MM-DD"),
            on_time: record.on_time,
            off_time: record.off_time,
            status: record.status,
          },
        ],
      });
    }
  }
  return trainees;
};

export const getAttendeceSummary = async (req, res, next) => {
  try {
    const [years] = await promisePool.query(
      "SELECT distinct year(attendences.date) as year FROM attendences "
    );
    let summary = [];
    for (const year of years) {
      let yearSummary = {
        year: year.year,
        months: [],
      };
      const [months] = await promisePool.query(
        "select distinct month(date) as month from attendences where year(date) = ?",
        [year.year]
      );
      for (const month of months) {
        yearSummary.months.push(month.month);
      }
      console.log(yearSummary);
      summary.push(yearSummary);
    }
    return res.status(200).json(summary);
  } catch (error) {
    next(error);
  }
};

/* const createAttendenceReport = (dataMap) => {
  const results = [];

  for (const [id, dates] of dataMap.entries()) {
    let count = 0;
    for (const [date, attendence] of dates) {
      console.log(date, attendence);
      if (attendence.on && attendence.off) {
        console.log("on and off exists");
        const on = moment(fractionalToTime(attendence.on), "HH:mm:ss");
        const off = moment(fractionalToTime(attendence.off), "HH:mm:ss");
        console.log(on, off);
        if (on.isBefore(startTime) && off.isAfter(endTime)) {
          count++;
          console.log("valid");
        } else {
          console.log("invalid");
        }
      }
    }
    results.push({
      id: id,
      count: count,
    });
  }
  const workBook = xlsx.utils.book_new();
  const sheet = xlsx.utils.json_to_sheet(results);
  xlsx.utils.book_append_sheet(workBook, sheet, "attendence");
  const buffer = write(workBook, { type: "buffer", bookType: "xlsx" });
  return buffer;
}; */

/* const createExcelFile = (dataMap) => {
  console.log(dataMap);
  const results = [];
  for (const [id, dates] of dataMap.entries()) {
    for (const [date, attendence] of dates.entries()) {
      const row = { id: id };
      row.date = dateConverter(date);
      row.on = fractionalToTime(attendence.on);
      row.off = fractionalToTime(attendence.off);
      results.push(row);
    }
  }
  console.log(results);
  if (!fs.existsSync(path.join(__dirname, "/sheets/"))) {
    fs.mkdirSync(path.join(__dirname, "/sheets/"), { recursive: true });
  }

  const workBook = xlsx.utils.book_new();
  const sheet = xlsx.utils.json_to_sheet(results);
  xlsx.utils.book_append_sheet(workBook, sheet, "attendence");
  const buffer = write(workBook, { type: "buffer", bookType: "xlsx" });
  return buffer;
  //xlsx.writeFile(workBook, path.join(__dirname, "/sheets/", "output.xlsx"));
}; */

/**
 * Get attendance count for specified trainees in a given month and year
 * @param {number} year 
 * @param {number} month
 * @param {number[]} traineeIds
 * @returns {Promise<Array>} Array of objects with trainee_id and attendance_count
 */
export const getAttendeceCount = async (year, month, traineeIds) => {
  try {

    if (!traineeIds || traineeIds.length === 0) {
      // Return empty array or handle it however you need
      return [];
    }

    const workingDays = await getWorkingDays(month, year);
    if (!workingDays || workingDays.length === 0) {
      return []; // No working days = no attendance
    }

    const traineeIdsPlaceholder = traineeIds.map(() => '?').join(',');
    const workingDaysPlaceholder = workingDays.map(() => '?').join(',');
    const [attCount] = await promisePool.query(
      `
      SELECT 
          trainee_id,
          COUNT(*) AS attendance_count
      FROM 
          attendences
      WHERE 
          status = 1
          AND DATE(date) IN (${workingDaysPlaceholder})
          AND trainee_id IN (${traineeIdsPlaceholder})
      GROUP BY 
          trainee_id;
      `,
      [...workingDays, ...traineeIds]
    );
    
    return attCount;

  } catch (error) {
    console.error('Error getting attendance count:', error);
    throw error;
  }
};

export const getDateSummary = async (req, res, next) => {
  try {
    const [years] = await promisePool.query(
      "SELECT distinct payment.year as year FROM payment ORDER BY year ASC;"
    );
    
    let summary = [];
    for (const year of years) {
      const [months] = await promisePool.query(
        "SELECT distinct payment.month as month FROM payment WHERE year = ? ORDER BY month ASC;",
        [year.year]
      );
      
      summary.push({
        year: year.year,
        months: months.map(m => m.month)
      });
    }
    
    return res.status(200).json(summary);
  } catch (error) {
    next(error);
  }
};