import express from "express";
import { index, remove, store } from "../Controllers/ProgramController.js";
import { Auth } from "../Core/AuthWrapper.js";
import { PROGRAMS_MODIFY } from "../Core/Permisions.js";

const router = express.Router();

router.post("/", await Auth([PROGRAMS_MODIFY]), store);
router.get("/", index);
router.delete("/:id", await Auth([PROGRAMS_MODIFY]), remove);

export default router;
