.small-calendar-container {
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
    
  }

/* Target the scrollbar for the FullCalendar container */
.small-calendar-container::-webkit-scrollbar {
    width: 10px; /* Width of the scrollbar */
}
  
  .small-calendar-container::-webkit-scrollbar-track {
    background: #888; /* Background of the scrollbar track */
  }
  
  .small-calendar-container::-webkit-scrollbar-thumb {
    background-color: #250e8a; /* Color of the scrollbar thumb */
    border-radius: 5px; /* Roundness of the thumb */
    border: 2px solid #f1f1f1; /* Optional: add a border for a 'gap' effect */
  }
  
  .small-calendar-container::-webkit-scrollbar-thumb:hover {
    background-color: #e60b0b; /* Color of the thumb on hover */
  }
  