import { Sequelize } from "sequelize";

export const sequalize = new Sequelize({
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  username: process.env.DB_USERNAME,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  dialect: "mysql",
  logging: false,
});

export const sync = async () => {
  try {
    await sequalize.authenticate();
    console.log("database connection has been established");
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const syncDatabaseRepetedly = async () => {
  await sync();
  setInterval(async () => {
    try {
      await sync();
    } catch (error) {
      console.log("failed to sync with the Database");
      throw error;
    }
  }, 1000 * 300);
};
