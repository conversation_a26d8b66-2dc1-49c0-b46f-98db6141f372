 /* ---------------------- */

 .toggle-sidebar-btn {
    font-size: 32px;
    cursor: pointer;
    color: #012970;
    position: fixed;
    transition: all 0.3s;
    padding: 15px;
    transform: none;
  }

  @media (max-width: 1199px) {
    .toggle-sidebar-btn{
      transform: translateY(-50px);
      transition: 0.5s;
      z-index: 991;
      top: 40px;
    }
  }

/*--------------------------------------------------------------
# Sidebar
--------------------------------------------------------------*/
.sidebar {
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    width: 275px;
    z-index: 996;
    transition: all 0.3s;
    padding: 20px;
    border-radius: 0px 20px 20px 0px;
    overflow-y: auto;
    overflow-x: hidden;
    scrollbar-width: thin;
    scrollbar-color: rgb(9,36,112) transparent;
    box-shadow: 0px 0px 20px rgba(1, 41, 112, 0.2);
    background-color: #fff;
  }

  .sidebar .bi-x-circle {
    font-size: 18px;
    padding-left: 10px;
    cursor: pointer;
    color: #ff0000;
  }

  
  @media (max-width: 1199px) {
    .sidebar {
      left: -300px;
    }
  }
  
  .sidebar::-webkit-scrollbar {
    width: 5px;
    height: 8px;
    background-color: #fff;
  }
  
  .sidebar::-webkit-scrollbar-thumb {
    background-color: #aab7cf;
  }
  
  @media (min-width: 1200px) {
  
    #main,
    #footer {
      margin-left: 300px;
    }
  }
  
  @media (max-width: 1199px) {
    .toggle-sidebar .sidebar {
      left: 0;
    }
  }
  
  @media (min-width: 1200px) {
  
    .toggle-sidebar #main,
    .toggle-sidebar #footer {
      margin-left: 40px;
    }
  
    .toggle-sidebar .sidebar {
      left: -300px;
    }
  }
  
  .sidebar-nav {
    padding: 0;
    margin: 0;
    list-style: none;
  }
  
  .sidebar-nav li {
    padding: 0;
    margin: 0;
    list-style: none;
  }
  
  .sidebar-nav .nav-item {
    margin-bottom: 5px;
  }

  .collapse.show + .nav-link .bi-chevron-down {
    transform: rotate(180deg);
    transition:all transform 0.3s ease;
  }

  .link-expand-btn .collapsed{
    transform: rotate(180deg);
    transition: transform 0.3s ease;
  }

  .sidebar-nav .nav-heading {
    font-size: 11px;
    text-transform: uppercase;
    color: #899bbd;
    font-weight: 600;
    margin: 10px 0 5px 15px;
  }

  .sidebar-nav  .enable-main {
    color: #4154f1;
  }

  .sub-link:hover{
    color: #4154f1;
  }

  .sidebar-nav .enable{
    color: #4154f1;
    background: #f6f9ff; 
    background: #d5e3ff;
  }

  .sidebar-nav .nav-link {
    display: flex;
    align-items: center;
    font-size: 15px;
    font-weight: 600;
    /* color: #4154f1; */
    transition: 0.3;
    /* background: #f6f9ff; */
    padding: 10px 15px;
    border-radius: 4px;
  }
  
  .sidebar-nav .nav-link i {
    font-size: 16px;
    margin-right: 10px;
    color: #4154f1;
  }
  
  .sidebar-nav .nav-link.disable {
    color: #012970;
    background: #fff;
  }
  
  .sidebar-nav .nav-link.disable i {
    color: #899bbd;
  }
  
  .sidebar-nav .nav-link:hover {
    color: #4154f1;
    /* background: #f6f9ff; */
    background: #d5e3ff;
  }
  
  .sidebar-nav .nav-link:hover i {
    color: #4154f1;
  }
  
  .sidebar-nav .nav-link .bi-chevron-down {
    margin-right: 0;
    transition: transform 0.2s ease-in-out;
  }
  
  .sidebar-nav .nav-link:not(.disable) .bi-chevron-down {
    transform: rotate(180deg);
  }
  
  .sidebar-nav .nav-content {
    padding: 5px 0 0 0;
    margin: 0;
    list-style: none;
  }
  
  .sidebar-nav .nav-content a {
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: 600;
    color: #012970;
    transition: 0.3;
    padding: 10px 0 10px 40px;
    transition: 0.3s;
  }
  
  .sidebar-nav .nav-content a i {
    font-size: 6px;
    margin-right: 8px;
    line-height: 0;
    border-radius: 50%;
  }
  
  .sidebar-nav .nav-content a:hover,

  .sidebar-nav .nav-content a.active {
    color: #4154f1;
  }
  
  .sidebar-nav .nav-content a.active i {
    background-color: #4154f1;
  }

  


  