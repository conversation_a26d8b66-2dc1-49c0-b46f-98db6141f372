import { z } from "zod";
import {Interview} from "../models/index.js";
import { sequalize } from "../DB/sequlize.js"; 
/*
export const store = async (req, res, next) => {
  const schema = z.object({
    NIC: z.string(),
    depId: z.coerce.number(),
    date: z.string().date(),
    name: z.string(),
  });
  try {
    const data = schema.parse(req.body);
    const result = await Interview.create({
      NIC: data.NIC,
      name: data.name,
      date: data.date,
      departmentId: data.depId,
    });
    res.status(201).json(result);
  } catch (error) {
    next(error);
  }
};
*/

export const store = async (req, res, next) => {
  // Define the schema for request validation
  const schema = z.object({
    nic: z.string(),
    email: z.string().email(),
    duration: z.string(),
    startDate: z.string().date(),
    name: z.string().nullish(),
    departments: z.array(z.object({
      department_id: z.coerce.number(),
      from: z.string().date().nullish(),
      to: z.string().date().nullish()
    }))
  });

  try {
    // Validate request data against schema
    const data = schema.parse(req.body);
    console.log("Request Data:", data);

    try {
      const InterviewRecords = await Promise.all(
        data.departments.map(dept => 
          Interview.create({
            NIC: data.nic,
            name: data.name,
            email: data.email,
            date: data.startDate,
            duration: data.duration,
            departmentId: dept.department_id,
            from: dept.from,
            to: dept.to
          })
        )
      );

      // Return the created interview with its departments
      return res.status(201).json(InterviewRecords);

    } catch (error) {
      console.error("Error creating interview records:", error);
      throw error;
    }

  } catch (error) {
    next(error);
  }
};

export const update = async (req, res, next) => {
  const schema = z.object({
    duration: z.string(),
    startDate: z.string().transform(val => {
      try {
        return new Date(val).toISOString();
      } catch (e) {
        return null;
      }
    }),
    name: z.string().nullish(),
    email: z.string().email(),
    departments: z.array(z.object({
      department_id: z.coerce.number(),
      from: z.string().transform(val => {
        if (!val) return null;
        try {
          return new Date(val).toISOString();
        } catch (e) {
          return null;
        }
      }).nullish(),
      to: z.string().transform(val => {
        if (!val) return null;
        try {
          return new Date(val).toISOString();
        } catch (e) {
          return null;
        }
      }).nullish()
    }))
  });

  try {
    const data = schema.parse({ ...req.body, nic: req.params.nic });

    // First delete the existing interview record
    await Interview.destroy({
      where: { NIC: req.params.nic }
    });

    // Create new records for each department
    const updatedInterviews = await Promise.all(
      data.departments.map(dept => 
        Interview.create({
          NIC: req.params.nic,
          name: data.name,
          email: data.email,
          date: data.startDate,
          duration: data.duration,
          departmentId: dept.department_id,
          from: dept.from,
          to: dept.to
        })
      )
    );

    return res.status(200).json({
      status: "success",
      data: updatedInterviews
    });

  } catch (error) {
    console.error("Update error:", error);
    next(error);
  }
};

export const updateByID = async (req, res, next) => {
  const schema = z.object({
    duration: z.string(),
    startDate: z.string().transform(val => {
      try {
        return new Date(val).toISOString();
      } catch (e) {
        return null;
      }
    }),
    name: z.string().nullish(),
    email: z.string().email(),
    departments: z.object({
      department_id: z.coerce.number(),
      from: z.string().transform(val => {
        if (!val) return null;
        try {
          return new Date(val).toISOString();
        } catch (e) {
          return null;
        }
      }).nullish(),
      to: z.string().transform(val => {
        if (!val) return null;
        try {
          return new Date(val).toISOString();
        } catch (e) {
          return null;
        }
      }).nullish()
    })
  });

  try {
    const data = schema.parse({ ...req.body, id: req.params.id });

    // Update the interview record by id
    const [affectedRows] = await Interview.update(
      {
        name: data.name,
        email: data.email,
        date: data.startDate,
        duration: data.duration,
        departmentId: data.departments.department_id,
        from: data.departments.from,
        to: data.departments.to
      },
      {
        where: { id: req.params.id }
      }
    );

    if (affectedRows === 0) {
      return res.status(404).json({
        status: "error",
        message: "Interview not found"
      });
    }

    // Fetch the updated record
    const updatedInterview = await Interview.findOne({ where: { id: req.params.id } });

    return res.status(200).json({
      status: "success",
      data: updatedInterview
    });

  } catch (error) {
    console.error("Update error:", error);
    next(error);
  }
};

/*
export const update = async (req, res, next) => {
  const schema = z.object({
    id: z.coerce.number(),
    NIC: z.string(),
    name: z.string(),
    date: z.string().date(),
  });
  try {
    const data = schema.parse({ id: req.params.id, ...req.body });
    const interview = await Interview.update(
      {
        name: data.name,
        NIC: data.NIC,
        date: data.date,
      },
      {
        where: {
          id: data.id,
        },
      }
    );
    return res.status(200).json(interview);
  } catch (error) {
    next(error);
  }
};

export const deleteRecord = async (req, res, next) => {
  const schema = z.object({
    id: z.coerce.number(),
  });
  console.log("delete record function", req.params);
  try {
    const data = schema.parse(req.params);
    const interview = await Interview.destroy({
      where: {
        id: data.id,
      },
    });
    return res.status(204).send();
  } catch (error) {
    console.log("delete Record", error);
    next();
  }
};
*/

export const deleteRecord = async (req, res, next) => {
  const schema = z.object({
    nic: z.coerce.string()
  });

  try {
    const data = schema.parse(req.params);
    
    // Delete the interview record
    const result = await Interview.destroy({
      where: {
        NIC: data.nic
      }
    });

    if (result === 0) {
      return res.status(404).json({
        status: "error",
        message: "Interview not found"
      });
    }

    return res.status(204).send();

  } catch (error) {
    console.log("Delete Record error:", error);
    next(error);
  }
};

export const deleteByID = async (req, res, next) => {
  const schema = z.object({
    id: z.coerce.number()
  });

  try {
    const data = schema.parse(req.params);
    
    // Delete the interview record
    const result = await Interview.destroy({
      where: {
        id: data.id
      }
    });

    if (result === 0) {
      return res.status(404).json({
        status: "error",
        message: "Interview not found"
      });
    }

    return res.status(204).send();

  } catch (error) {
    console.log("Delete Record error:", error);
    next(error);
  }
};

const transformInterviewData = (data) => {
  const transformed = [];

  const interviewMap = new Map();

  data.forEach((item) => {
    const key = `${item.NIC}-${item.date}`;
    if (!interviewMap.has(key)) {
      interviewMap.set(key, {
        id: item.id,
        NIC: item.NIC,
        name: item.name,
        email: item.email,
        date: item.date,
        duration: item.duration || "",
        createdAt: item.createdAt,
        updatedAt: item.updatedAt,
        departments: [],
      });
    }

    interviewMap.get(key).departments.push({
      id: item.departmentId,
      fromDate: item.from || "",
      toDate: item.to || "",
    });
  });

  interviewMap.forEach((value) => {
    transformed.push(value);
  });

  return transformed;
}


export const show = async (req, res) => {
  try {
    const result = await Interview.findAll({raw: true});

    const transformedData = transformInterviewData(result);

    // Always return 200 with a consistent structure
    return res.status(200).json({
      InterviewDetails: transformedData || [], // Ensure it's always an array
      status: "success",
      message: result.length ? "Interviews loaded" : "No interviews found"
    });

  } catch (error) {
    console.error("Backend Error:", error);
    // Return structured error response
    return res.status(500).json({
      InterviewDetails: [],
      status: "error",
      message: "Failed to load interviews"
    });
  }
};
/*
export const show = async (req, res, next) => {
  const schema = z.object({
    NIC: z.string(),
  });
  try {
    console.log(req.params);
    const data = schema.parse(req.params);
    const result = await Interview.findOne({
      where: {
        NIC: data.NIC,
      },
    });
    return res.status(200).json(result);
  } catch (error) {
    next(error);
  }
};
*/
export const showTest = async (req, res, next) => {
  const schema = z.object({
    NIC: z.string(),
  });
  try {
    console.log(req.params);
    const data = schema.parse(req.params);
    const result = await Interview.findOne({
      where: {
        NIC: data.NIC,
      },
    });
    return res.status(200).json(result);
  } catch (error) {
    next(error);
  }
};
