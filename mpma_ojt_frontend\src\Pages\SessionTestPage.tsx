import React from 'react';
import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Badge } from 'react-bootstrap';
import { useSession } from '../contexts/SessionContext';
import api from '../api';

const SessionTestPage: React.FC = () => {
  const {
    isAuthenticated,
    user,
    sessionTimeLeft,
    isSessionWarningVisible,
    logout
  } = useSession();

  // Format time for display
  const formatTime = (milliseconds: number) => {
    const totalSeconds = Math.floor(milliseconds / 1000);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;
    
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  // Test API call to check authentication
  const testApiCall = async () => {
    try {
      const response = await api.get('auth');
      alert('API call successful! User authenticated.');
      console.log('User data:', response.data);
    } catch (error) {
      console.error('API call failed:', error);
      alert('API call failed! Check console for details.');
    }
  };

  // Test protected API call
  const testProtectedApiCall = async () => {
    try {
      const response = await api.get('api/trainee');
      alert('Protected API call successful!');
      console.log('Trainees data:', response.data);
    } catch (error) {
      console.error('Protected API call failed:', error);
      alert('Protected API call failed! Check console for details.');
    }
  };

  const getTimeColor = (milliseconds: number) => {
    const minutes = Math.floor(milliseconds / (1000 * 60));
    if (minutes <= 5) return 'danger';
    if (minutes <= 15) return 'warning';
    return 'success';
  };

  return (
    <div className="container mt-4">
      <h2>Session Management Test Page</h2>
      
      <div className="row">
        <div className="col-md-6">
          <Card>
            <Card.Header>
              <h5>Session Status</h5>
            </Card.Header>
            <Card.Body>
              <div className="mb-3">
                <strong>Authentication Status:</strong>{' '}
                <Badge bg={isAuthenticated ? 'success' : 'danger'}>
                  {isAuthenticated ? 'Authenticated' : 'Not Authenticated'}
                </Badge>
              </div>
              
              <div className="mb-3">
                <strong>User:</strong> {user?.name || 'Not logged in'}
              </div>
              
              <div className="mb-3">
                <strong>Session Time Remaining:</strong>{' '}
                <Badge bg={getTimeColor(sessionTimeLeft)}>
                  {formatTime(sessionTimeLeft)}
                </Badge>
              </div>
              
              <div className="mb-3">
                <strong>Warning Visible:</strong>{' '}
                <Badge bg={isSessionWarningVisible ? 'warning' : 'secondary'}>
                  {isSessionWarningVisible ? 'Yes' : 'No'}
                </Badge>
              </div>
            </Card.Body>
          </Card>
        </div>
        
        <div className="col-md-6">
          <Card>
            <Card.Header>
              <h5>Test Actions</h5>
            </Card.Header>
            <Card.Body>
              <div className="d-grid gap-2">
                <Button variant="primary" onClick={testApiCall}>
                  Test Auth API Call
                </Button>
                
                <Button variant="info" onClick={testProtectedApiCall}>
                  Test Protected API Call
                </Button>

                <Button variant="danger" onClick={logout}>
                  Logout
                </Button>
              </div>
            </Card.Body>
          </Card>
        </div>
      </div>
      
      <div className="row mt-4">
        <div className="col-12">
          <Alert variant="info">
            <h6>Testing Instructions:</h6>
            <ul className="mb-0">
              <li>Watch the session timer count down</li>
              <li>A warning should appear when 5 minutes remain</li>
              <li>The session should automatically expire after the set duration</li>
              <li>Test API calls to verify authentication works</li>
              <li>Check browser console for detailed logs</li>
            </ul>
          </Alert>
        </div>
      </div>
      
      <div className="row mt-3">
        <div className="col-12">
          <Alert variant="warning">
            <strong>Note:</strong> For testing purposes, you can modify the session duration constants 
            in <code>SessionContext.tsx</code> to shorter values (e.g., 2 minutes) to test the timeout 
            functionality more quickly.
          </Alert>
        </div>
      </div>
    </div>
  );
};

export default SessionTestPage;
