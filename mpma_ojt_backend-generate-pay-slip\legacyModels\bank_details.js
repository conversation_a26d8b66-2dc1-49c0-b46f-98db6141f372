const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('bank_details', {
    id: {
      autoIncrement: true,
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true
    },
    trainee_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      unique: "trainee_id"
    },
    branch_code: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    acc_no: {
      type: DataTypes.STRING(10),
      allowNull: false,
      unique: "acc_no_UNIQUE"
    }
  }, {
    sequelize,
    tableName: 'bank_details',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "id" },
        ]
      },
      {
        name: "acc_no_UNIQUE",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "acc_no" },
        ]
      },
      {
        name: "trainee_id",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "trainee_id" },
        ]
      },
    ]
  });
};
