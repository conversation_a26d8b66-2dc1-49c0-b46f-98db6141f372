import { User } from "./User.js";
import { AcessLevel } from "./AccessLevel.js";
import { Attendence } from "./Attendence.js";
import { sequalize } from "../DB/sequlize.js";
import { DataTypes } from "sequelize";
import { Section } from "./Section.js";
import { Department } from "./Department.js";
import { Interview } from "./Interview.js";
import { Trainee } from "./trainee.js";
import { Schedule } from "./Schedule.js";

User.hasMany(AcessLevel, { onDelete: "CASCADE" });
AcessLevel.belongsTo(User);

Department.hasMany(Section);
Section.belongsTo(Department);

Department.hasMany(Interview);
Interview.belongsTo(Department);

Department.hasMany(Schedule, { foreignKey: "department_id" });
Schedule.belongsTo(Department, { foreignKey: "department_id" });
Trainee.hasMany(Schedule, { foreignKey: "trainee_id" });
Schedule.belongsTo(Trainee, { foreignKey: "trainee_id" });

export { AcessLevel, User, Attendence, Department, Schedule, Section, Interview, Trainee };
