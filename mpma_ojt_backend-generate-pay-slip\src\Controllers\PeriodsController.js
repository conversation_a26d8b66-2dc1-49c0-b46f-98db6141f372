import { z } from "zod";
import { promisePool } from "../DB/Database.js";

export const index = async (req, res, next) => {
  try {
    const [periods] = await promisePool.query("select * from periods");
    return res.status(200).json(periods);
  } catch (e) {
    next(e);
  }
};

const schema = z.object({
  name: z.string().min(1, "Name is required"),
  years: z.coerce
    .number({ message: "Year must be a number " })
    .gt(0, { message: "Year must be grater than 0" })
    .int({ message: "Must be a whole number" })
    .optional(),
  months: z.coerce
    .number({ message: "Months must be a number " })
    .gt(0, { message: "Month must be Greater than 0" })
    .int({ message: "Must be a whole number" })
    .optional(),
  weeks: z.coerce
    .number({ message: "Weeks must be a number" })
    .gt(0, { message: "Weeks must be Greater than 0" })
    .int({ message: "Must be a whole number" })
    .optional(),
  days: z.coerce
    .number({ message: "Days must be a number" })
    .gt(0, { message: "Weeks must be Greater than 0" })
    .int({ message: "Must be a whole number" })
    .optional(),
});

export const store = async (req, res, next) => {
  try {
    const data = schema.parse(req.body);
    //return res.status(200).json(data);
    if (data.months || data.years || data.weeks || data.days) {
      const [result] = await promisePool.query(
        "INSERT INTO `periods`(`name`,`year`,`Months`,`weeks`,`days`) VALUES (?,?,?,?,?)",
        [data.name, data.years, data.months, data.weeks, data.days]
      );
      return res.status(200).json(result);
    } else {
      throw new Error("duration not recieved");
    }

    return res.status(200).json(result);
  } catch (e) {
    next(e);
  }
};
