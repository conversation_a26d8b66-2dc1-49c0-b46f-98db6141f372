import moment from "moment";
import { promisePool } from "../DB/Database.js";
import { z } from "zod";

export const index = async (req, res, next) => {
  try {
    const [holidays] = await promisePool.query("SELECT * FROM `holidays`");
    const data = holidays.map((holiday) => {
      return {
        id: holiday.id,
        start: moment(holiday.start_date).format("YYYY-MM-DD"),
        end: moment(holiday.end_date).format("YYYY-MM-DD"),
        title: holiday.description,
        color: "red",
      };
    });
    res.status(200).json(data);
  } catch (error) {
    next(error);
  }
};

export const create = async (req, res, next) => {
  const schema = z.object({
    startDate: z.string().date(),
    endDate: z.string().date(),
    description: z.string(),
  });
  try {
    //get the start date and end date from the client and insert a new record in the holidays table
    const data = schema.parse(req.body);
    const [result] = await promisePool.query(
      "INSERT INTO `holidays`(`start_date`, `end_date`, `description`) VALUES (?,?,?)",
      [data.startDate, data.endDate, data.description]
    );
    console.log(result);
    res.status(201).json(result);
  } catch (error) {
    next(error);
  }
};

export const retrieveHolidays = async (req, res, next) => {
  const schema = z.object({
    month: z.coerce.number().gt(0).lt(13),
    year: z.coerce.number().gt(2000),
  });
  try {
    const data = schema.parse(req.params);
    const momentHolidays = await getHolidaysArray(data.month, data.year);
    let holidays = momentHolidays.map((momentDate) => momentDate.format("YYYY-MM-DD"));
    return res.staus(200).json(holidays);
  } catch (error) {
    next(error);
  }
};

export const remove = async (req, res, next) => {
  const schema = z.object({
    start_date: z.string().date(),
    end_date: z.string().date(),
  });
  try {
    const data = schema.parse(req.query);
    const result = await promisePool.query(
      "DELETE FROM `holidays` WHERE start_date=? and end_date=?",
      [data.start_date, data.end_date]
    );
    res.status(204).send();
  } catch (error) {
    next(error);
  }
};

export const retriveWorkingDays = async (req, res, next) => {
  const schema = z.object({
    month: z.coerce.number().gt(0).lt(13),
    year: z.coerce.number().gt(2000),
  });
  try {
    const data = schema.parse(req.params);
    const workingDays = await getWorkingDays(data.month, data.year);
    return res.status(200).json(workingDays);
  } catch (error) {
    next(error);
  }
};

/**
 * retrives the holidays of the specific year and month from the database
 * converting the event format of the database to array of single days
 * @param {number} month
 * @param {number} year
 * @returns {Promise<Array<moment.Moment>>} array of moment objects of the holidays
 */
export const getHolidaysArray = async (month, year) => {
  try {
    let holidays = [];
    /* 
    all the holidays in the respective year and month
    ["2024-12-01"]
  */
    const [holidayEvents] = await promisePool.query(
      `SELECT 
    start_date,end_date- interval 1 day as end_date,description 
    FROM holidays 
    where (year(start_date)=? or year(end_date)=?)
    and (month(start_date)=? or month(end_date)=?);`,
      [year, year, month, month]
    );

    holidayEvents.forEach((event) => {
      const start = moment(event.start_date).clone();
      const end = moment(event.end_date).clone();
      let currentDay = start;

      //looping through each day of the event
      while (currentDay.isSameOrBefore(end)) {
        if (currentDay.year() == year && currentDay.month() + 1 == month) {
          holidays.push(currentDay.clone());
        }
        currentDay.add(1, "day");
      }
    });
    return holidays;
  } catch (error) {
    console.log("Failed to get Holidays Array");
    throw error;
  }
};

/**
 *get the working days excluding all holidays as array of string
 *Date is represented in YYYY-MM-DD format
 * @param {*} month
 * @param {*} year
 * @returns {Promise<Array<string>} array of dates
 */
 
export const getWorkingDays = async (month, year) => {
  try {
    const holidays = await getHolidaysArray(month, year); //get the holidays as a array of holiday dates
    const startOfTheMonth = moment([year, month - 1]);
    const endOFTheMonth = startOfTheMonth.clone().endOf("month");

    let currentDay = startOfTheMonth;
    let holidaysCount = 0;
    let workDaysCount = 0;
    const workDays = []; //initialize the working days array

    //looping through the month of the year
    while (currentDay.isSameOrBefore(endOFTheMonth)) {
      // Check if it's a weekday (Monday to Friday)
      if (currentDay.isoWeekday() <= 5) {
        // Check if the current day is a holiday
        if (holidays.some((holiday) => holiday.isSame(currentDay, "day"))) {
          holidaysCount++;
        } else {
          workDaysCount++;
          workDays.push(currentDay.format("YYYY-MM-DD"));
        }
      }
      currentDay.add(1, "day"); // Move to the next day after processing (whether it's a holiday or not)
    }
    console.log("holidays count- " + holidaysCount);
    console.log("workDays count - " + workDaysCount);
    return workDays;
  } catch (error) {
    console.log("Failed to get Working Days Array");
    throw error;
  }
};

/**
 *
 * @returns {Promise<Array<string>>} array of mondays excluding holidays
 */
export const getMondays = async () => {
  try {
    const date = moment(new Date()).clone();
    let mondays = [];
    for (let index = 1; index <= 6; index++) {
      const workingDays = await getWorkingDays(date.month() + 1, date.year());
      workingDays.forEach((day) => {
        const momentDay = moment(day);
        if (momentDay.isoWeekday() == 1) {
          if (momentDay.isAfter(date)) {
            mondays.push(day);
          }
        }
      });
      date.add(1, "M");
    }
    return mondays;
  } catch (error) {
    console.log("error : CalenderController.getMondays() ");
    throw error;
  }
};
