  /*--------------------------------------------------------------
# Header
--------------------------------------------------------------*/
.logo {
    line-height: 1;
    pointer-events: none;
    /* user-select: none; */
  }
  
  @media (min-width: 1200px) {
    .logo {
      width: 280px;
    }
  }
  
  .logo img {
    max-height: 50px;
    margin-right: 6px;
  }
  
  .logo span {
    font-size: 26px;
    font-weight: 700;
    color: #012970;
    font-family: "Nunito", sans-serif;
  }
  
  .header {
    transition: all 0.5s;
    z-index: 990;
    height: 55px  ;
    /* box-shadow: 0px 2px 20px rgba(1, 41, 112, 0.1); */
    /* background-color: #fff; */
    background-color: #f1f6ff;
    padding-left: 20px;
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
  }

  @media (max-width: 1199px) {
    #name1 {
      transform: translateX(35px);
      transition: 0.5s;
    }
  }
  
  .header .notifi-profile {
    box-shadow: 0px 2px 20px rgba(1, 41, 112, 0.2);
    background-color: #fff;
    border-radius: 30px;
    margin-right: 10px;
    padding: 8px 5px 8px 18px;
  }
  
  
  
  /*--------------------------------------------------------------
  # Header Nav
  --------------------------------------------------------------*/
  .header-nav ul {
    list-style: none;
  }
  
  .header-nav>ul {
    margin: 0;
    padding: 0;
  }
  
  .header-nav .nav-icon {
    font-size: 22px;
    color: #012970;
    margin-right: 25px;
    position: relative;
  }
  
  .header-nav .nav-profile {
    color: #012970;
  }
  
  .header-nav .nav-profile img {
    max-height: 36px;
    user-select: none;
  }
  
  .header-nav .nav-profile span {
    font-size: 14px;
    font-weight: 600;
  }
  
  .header-nav .badge-number {
    position: absolute;
    inset: -2px -5px auto auto;
    font-weight: normal;
    font-size: 12px;
    padding: 3px 6px;
  }
  
  .header-nav .notifications {
    inset: 8px -15px auto auto !important;
    height: 525px;
    overflow-y: auto;
    scroll-behavior: smooth;
    scrollbar-width: thin;
    scrollbar-color: rgb(9,36,112) transparent;
    box-shadow: 0px 2px 20px rgba(1, 41, 112, 0.2);
    border: 1px solid #0008ff87;
    border-radius: 30px;
  }
  
  .notifications .notifi-count{
    font-weight: bold;
  }
  
  .notifications .dropdown-divider{
    margin-left: 10px;
    margin-right: 10px;
    background: #0014ff26;
  }
  
  .header-nav .notifications .notification-item {
    display: flex;
    align-items: center;
    padding: 0px 5px 0px 5px;
    transition: all 0.3s;
  }
  
  .header-nav .notifications .notification-item i {
    margin: 0 20px 0 10px;
    font-size: 24px;
  }
  
  .header-nav .notifications .notification-item h4 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 5px;
  }
  
  .header-nav .notifications .notification-item p {
    font-size: 13px;
    margin-bottom: 3px;
    color: #919191;
  }
  
  .header-nav .notifications .notification-item:hover {
    background-color: #f6f9ff;
  }
  
  .header-nav .messages {
    inset: 8px -15px auto auto !important;
  }
  
  .header-nav .messages .message-item {
    padding: 15px 10px;
    transition: 0.3s;
  }
  
  .header-nav .messages .message-item a {
    display: flex;
  }
  
  .header-nav .messages .message-item img {
    margin: 0 20px 0 10px;
    max-height: 40px;
  }
  
  .header-nav .messages .message-item h4 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 5px;
    color: #444444;
  }
  
  .header-nav .messages .message-item p {
    font-size: 13px;
    margin-bottom: 3px;
    color: #919191;
  }
  
  .header-nav .messages .message-item:hover {
    background-color: #f6f9ff;
  }
  
  .header-nav .profile {
    min-width: 240px;
    padding-bottom: 0;
    top: 8px !important;
    z-index: 900;
  }