Sub Macro1()
    Cells.Select
    With Selection.Font
        .Name = "Times New Roman"
        .Size = 12
        .Strikethrough = False
        .Superscript = False
        .Subscript = False
        .OutlineFont = False
        .Shadow = False
        .Underline = xlUnderlineStyleNone
        .ThemeColor = xlThemeColorLight1
        .TintAndShade = 0
        .ThemeFont = xlThemeFontNone
    End With
    With Selection.Font
        .Name = "Times New Roman"
        .Size = 11
        .Strikethrough = False
        .Superscript = False
        .Subscript = False
        .OutlineFont = False
        .Shadow = False
        .Underline = xlUnderlineStyleNone
        .ThemeColor = xlThemeColorLight1
        .TintAndShade = 0
        .ThemeFont = xlThemeFontNone
    End With
    Columns("A:A").Select
    With Selection
        .HorizontalAlignment = xlCenter
        .VerticalAlignment = xlBottom
        .WrapText = False
        .Orientation = 0
        .AddIndent = False
        .IndentLevel = 0
        .ShrinkToFit = False
        .ReadingOrder = xlContext
        .MergeCells = False
    End With
    With Selection
        .HorizontalAlignment = xlCenter
        .VerticalAlignment = xlCenter
        .WrapText = False
        .Orientation = 0
        .AddIndent = False
        .IndentLevel = 0
        .ShrinkToFit = False
        .ReadingOrder = xlContext
        .MergeCells = False
    End With
    Columns("B:B").Select
    With Selection
        .HorizontalAlignment = xlLeft
        .VerticalAlignment = xlBottom
        .WrapText = False
        .Orientation = 0
        .AddIndent = False
        .IndentLevel = 0
        .ShrinkToFit = False
        .ReadingOrder = xlContext
        .MergeCells = False
    End With
    Columns("D:D").Select
    With Selection
        .HorizontalAlignment = xlGeneral
        .VerticalAlignment = xlCenter
        .WrapText = False
        .Orientation = 0
        .AddIndent = False
        .IndentLevel = 0
        .ShrinkToFit = False
        .ReadingOrder = xlContext
        .MergeCells = False
    End With
    With Selection
        .HorizontalAlignment = xlCenter
        .VerticalAlignment = xlCenter
        .WrapText = False
        .Orientation = 0
        .AddIndent = False
        .IndentLevel = 0
        .ShrinkToFit = False
        .ReadingOrder = xlContext
        .MergeCells = False
    End With
    Range("A1:E1").Select
    Range("E1").Activate
    Selection.Font.Bold = True
    With Selection
        .VerticalAlignment = xlCenter
        .WrapText = False
        .Orientation = 0
        .AddIndent = False
        .IndentLevel = 0
        .ShrinkToFit = False
        .ReadingOrder = xlContext
        .MergeCells = False
    End With
    With Selection
        .HorizontalAlignment = xlCenter
        .VerticalAlignment = xlCenter
        .WrapText = False
        .Orientation = 0
        .AddIndent = False
        .IndentLevel = 0
        .ShrinkToFit = False
        .ReadingOrder = xlContext
        .MergeCells = False
    End With
    Columns("E:E").Select
    With Selection
        .VerticalAlignment = xlBottom
        .WrapText = False
        .Orientation = 0
        .AddIndent = False
        .IndentLevel = 0
        .ShrinkToFit = False
        .ReadingOrder = xlContext
        .MergeCells = False
    End With
    With Selection
        .VerticalAlignment = xlCenter
        .WrapText = False
        .Orientation = 0
        .AddIndent = False
        .IndentLevel = 0
        .ShrinkToFit = False
        .ReadingOrder = xlContext
        .MergeCells = False
    End With
    Columns("B:C").Select
    With Selection
        .VerticalAlignment = xlBottom
        .WrapText = False
        .Orientation = 0
        .AddIndent = False
        .IndentLevel = 0
        .ShrinkToFit = False
        .ReadingOrder = xlContext
        .MergeCells = False
    End With
    With Selection
        .VerticalAlignment = xlCenter
        .WrapText = False
        .Orientation = 0
        .AddIndent = False
        .IndentLevel = 0
        .ShrinkToFit = False
        .ReadingOrder = xlContext
        .MergeCells = False
    End With
    Columns("F:W").Select
    With Selection
        .HorizontalAlignment = xlCenter
        .VerticalAlignment = xlBottom
        .WrapText = False
        .Orientation = 0
        .AddIndent = False
        .IndentLevel = 0
        .ShrinkToFit = False
        .ReadingOrder = xlContext
        .MergeCells = False
    End With
    With Selection
        .HorizontalAlignment = xlCenter
        .VerticalAlignment = xlCenter
        .WrapText = False
        .Orientation = 0
        .AddIndent = False
        .IndentLevel = 0
        .ShrinkToFit = False
        .ReadingOrder = xlContext
        .MergeCells = False
    End With
    Range("X1").Select
    ActiveCell.FormulaR1C1 = "1"
    Range("X1").Select
    Selection.Copy
    Range("F1:W1").Select
    Selection.PasteSpecial Paste:=xlPasteAll, Operation:=xlMultiply, _
        SkipBlanks:=False, Transpose:=False
    Application.CutCopyMode = False
    Selection.NumberFormat = "m/d/yyyy"
    With Selection
        .HorizontalAlignment = xlGeneral
        .VerticalAlignment = xlBottom
        .WrapText = False
        .Orientation = 90
        .AddIndent = False
        .IndentLevel = 0
        .ShrinkToFit = False
        .ReadingOrder = xlContext
        .MergeCells = False
    End With
    Range("X1").Select
    Selection.ClearContents
    Columns("F:W").Select
    With Selection
        .HorizontalAlignment = xlCenter
        .WrapText = False
        .Orientation = 0
        .AddIndent = False
        .IndentLevel = 0
        .ShrinkToFit = False
        .ReadingOrder = xlContext
        .MergeCells = False
    End With
    With Selection
        .HorizontalAlignment = xlCenter
        .VerticalAlignment = xlCenter
        .WrapText = False
        .Orientation = 0
        .AddIndent = False
        .IndentLevel = 0
        .ShrinkToFit = False
        .ReadingOrder = xlContext
        .MergeCells = False
    End With
    Range("D2:D351").Select
    Selection.NumberFormat = "[$-en-US]d-mmm-yyyy;@"
    Selection.TextToColumns Destination:=Range("D2"), DataType:=xlDelimited, _
        TextQualifier:=xlDoubleQuote, ConsecutiveDelimiter:=False, Tab:=True, _
        Semicolon:=False, Comma:=False, Space:=False, Other:=False, FieldInfo _
        :=Array(1, 1), TrailingMinusNumbers:=True
    ActiveWindow.ScrollColumn = 3
    ActiveWindow.ScrollColumn = 2
    ActiveWindow.ScrollColumn = 1
    ActiveWindow.ScrollColumn = 2
    ActiveWindow.ScrollColumn = 3
    ActiveWindow.ScrollColumn = 4
    Range("X1").Select
    ActiveCell.FormulaR1C1 = "ATTN" & Chr(10) & "TOTAL"
    Range("Y1").Select
    ActiveCell.FormulaR1C1 = "PAY" & Chr(10) & "AMOUNT" & Chr(10) & "(RS)"
    Range("X1:Y1").Select
    Selection.Font.Bold = True
    Range("X1").Select
    With Selection
        .HorizontalAlignment = xlGeneral
        .VerticalAlignment = xlBottom
        .WrapText = True
        .Orientation = 90
        .AddIndent = False
        .IndentLevel = 0
        .ShrinkToFit = False
        .ReadingOrder = xlContext
        .MergeCells = False
    End With
    Range("X2").Select
    Application.CutCopyMode = False
    ActiveCell.FormulaR1C1 = "=SUM(RC[-18]:RC[-1])"
    Range("X2").Select
    Selection.AutoFill Destination:=Range("X2:X351"), Type:=xlFillDefault
    Range("X2:X351").Select
    ActiveWindow.ScrollRow = 331
    ActiveWindow.ScrollRow = 328
    ActiveWindow.ScrollRow = 326
    ActiveWindow.ScrollRow = 321
    ActiveWindow.ScrollRow = 316
    ActiveWindow.ScrollRow = 310
    ActiveWindow.ScrollRow = 303
    ActiveWindow.ScrollRow = 297
    ActiveWindow.ScrollRow = 289
    ActiveWindow.ScrollRow = 282
    ActiveWindow.ScrollRow = 273
    ActiveWindow.ScrollRow = 264
    ActiveWindow.ScrollRow = 256
    ActiveWindow.ScrollRow = 247
    ActiveWindow.ScrollRow = 238
    ActiveWindow.ScrollRow = 222
    ActiveWindow.ScrollRow = 205
    ActiveWindow.ScrollRow = 178
    ActiveWindow.ScrollRow = 169
    ActiveWindow.ScrollRow = 162
    ActiveWindow.ScrollRow = 153
    ActiveWindow.ScrollRow = 145
    ActiveWindow.ScrollRow = 131
    ActiveWindow.ScrollRow = 125
    ActiveWindow.ScrollRow = 117
    ActiveWindow.ScrollRow = 103
    ActiveWindow.ScrollRow = 96
    ActiveWindow.ScrollRow = 89
    ActiveWindow.ScrollRow = 81
    ActiveWindow.ScrollRow = 71
    ActiveWindow.ScrollRow = 65
    ActiveWindow.ScrollRow = 57
    ActiveWindow.ScrollRow = 50
    ActiveWindow.ScrollRow = 45
    ActiveWindow.ScrollRow = 40
    ActiveWindow.ScrollRow = 35
    ActiveWindow.ScrollRow = 31
    ActiveWindow.ScrollRow = 27
    ActiveWindow.ScrollRow = 25
    ActiveWindow.ScrollRow = 23
    ActiveWindow.ScrollRow = 21
    ActiveWindow.ScrollRow = 18
    ActiveWindow.ScrollRow = 15
    ActiveWindow.ScrollRow = 14
    ActiveWindow.ScrollRow = 13
    ActiveWindow.ScrollRow = 12
    ActiveWindow.ScrollRow = 11
    ActiveWindow.ScrollRow = 10
    ActiveWindow.ScrollRow = 9
    ActiveWindow.ScrollRow = 8
    ActiveWindow.ScrollRow = 7
    ActiveWindow.ScrollRow = 6
    ActiveWindow.ScrollRow = 5
    ActiveWindow.ScrollRow = 4
    ActiveWindow.ScrollRow = 3
    ActiveWindow.ScrollRow = 1
    Range("Y2").Select
    ActiveCell.FormulaR1C1 = "=RC[-1]*500"
    Range("Y2").Select
    Selection.AutoFill Destination:=Range("Y2:Y351"), Type:=xlFillDefault
    Range("Y2:Y351").Select
    Range("Y352").Select
    Application.CutCopyMode = False
    ActiveCell.FormulaR1C1 = "=SUM(R[-350]C:R[-1]C)"
    Range("Y353").Select
    ActiveWindow.ScrollRow = 331
    ActiveWindow.ScrollRow = 330
    ActiveWindow.ScrollRow = 327
    ActiveWindow.ScrollRow = 324
    ActiveWindow.ScrollRow = 320
    ActiveWindow.ScrollRow = 314
    ActiveWindow.ScrollRow = 307
    ActiveWindow.ScrollRow = 296
    ActiveWindow.ScrollRow = 286
    ActiveWindow.ScrollRow = 275
    ActiveWindow.ScrollRow = 263
    ActiveWindow.ScrollRow = 253
    ActiveWindow.ScrollRow = 243
    ActiveWindow.ScrollRow = 234
    ActiveWindow.ScrollRow = 227
    ActiveWindow.ScrollRow = 219
    ActiveWindow.ScrollRow = 214
    ActiveWindow.ScrollRow = 208
    ActiveWindow.ScrollRow = 201
    ActiveWindow.ScrollRow = 192
    ActiveWindow.ScrollRow = 187
    ActiveWindow.ScrollRow = 179
    ActiveWindow.ScrollRow = 171
    ActiveWindow.ScrollRow = 163
    ActiveWindow.ScrollRow = 155
    ActiveWindow.ScrollRow = 148
    ActiveWindow.ScrollRow = 140
    ActiveWindow.ScrollRow = 131
    ActiveWindow.ScrollRow = 124
    ActiveWindow.ScrollRow = 116
    ActiveWindow.ScrollRow = 108
    ActiveWindow.ScrollRow = 99
    ActiveWindow.ScrollRow = 90
    ActiveWindow.ScrollRow = 83
    ActiveWindow.ScrollRow = 76
    ActiveWindow.ScrollRow = 70
    ActiveWindow.ScrollRow = 64
    ActiveWindow.ScrollRow = 60
    ActiveWindow.ScrollRow = 56
    ActiveWindow.ScrollRow = 48
    ActiveWindow.ScrollRow = 45
    ActiveWindow.ScrollRow = 43
    ActiveWindow.ScrollRow = 41
    ActiveWindow.ScrollRow = 39
    ActiveWindow.ScrollRow = 37
    ActiveWindow.ScrollRow = 35
    ActiveWindow.ScrollRow = 34
    ActiveWindow.ScrollRow = 32
    ActiveWindow.ScrollRow = 31
    ActiveWindow.ScrollRow = 29
    ActiveWindow.ScrollRow = 28
    ActiveWindow.ScrollRow = 27
    ActiveWindow.ScrollRow = 25
    ActiveWindow.ScrollRow = 23
    ActiveWindow.ScrollRow = 21
    ActiveWindow.ScrollRow = 18
    ActiveWindow.ScrollRow = 15
    ActiveWindow.ScrollRow = 11
    ActiveWindow.ScrollRow = 9
    ActiveWindow.ScrollRow = 7
    ActiveWindow.ScrollRow = 5
    ActiveWindow.ScrollRow = 2
    ActiveWindow.ScrollRow = 1
    Columns("X:Y").Select
    With Selection
        .HorizontalAlignment = xlGeneral
        .VerticalAlignment = xlCenter
        .Orientation = 0
        .AddIndent = False
        .IndentLevel = 0
        .ShrinkToFit = False
        .ReadingOrder = xlContext
        .MergeCells = False
    End With
    With Selection
        .HorizontalAlignment = xlCenter
        .VerticalAlignment = xlCenter
        .Orientation = 0
        .AddIndent = False
        .IndentLevel = 0
        .ShrinkToFit = False
        .ReadingOrder = xlContext
        .MergeCells = False
    End With
    Range("X2:Y351").Select
    Selection.NumberFormat = "_(* #,##0_);_(* (#,##0);_(* ""-""_);_(@_)"
    ActiveWindow.ScrollRow = 3
    ActiveWindow.ScrollRow = 6
    ActiveWindow.ScrollRow = 11
    ActiveWindow.ScrollRow = 18
    ActiveWindow.ScrollRow = 24
    ActiveWindow.ScrollRow = 31
    ActiveWindow.ScrollRow = 37
    ActiveWindow.ScrollRow = 43
    ActiveWindow.ScrollRow = 50
    ActiveWindow.ScrollRow = 56
    ActiveWindow.ScrollRow = 61
    ActiveWindow.ScrollRow = 67
    ActiveWindow.ScrollRow = 73
    ActiveWindow.ScrollRow = 78
    ActiveWindow.ScrollRow = 85
    ActiveWindow.ScrollRow = 91
    ActiveWindow.ScrollRow = 98
    ActiveWindow.ScrollRow = 104
    ActiveWindow.ScrollRow = 112
    ActiveWindow.ScrollRow = 118
    ActiveWindow.ScrollRow = 125
    ActiveWindow.ScrollRow = 146
    ActiveWindow.ScrollRow = 153
    ActiveWindow.ScrollRow = 159
    ActiveWindow.ScrollRow = 163
    ActiveWindow.ScrollRow = 169
    ActiveWindow.ScrollRow = 175
    ActiveWindow.ScrollRow = 180
    ActiveWindow.ScrollRow = 185
    ActiveWindow.ScrollRow = 190
    ActiveWindow.ScrollRow = 195
    ActiveWindow.ScrollRow = 201
    ActiveWindow.ScrollRow = 206
    ActiveWindow.ScrollRow = 211
    ActiveWindow.ScrollRow = 216
    ActiveWindow.ScrollRow = 220
    ActiveWindow.ScrollRow = 225
    ActiveWindow.ScrollRow = 228
    ActiveWindow.ScrollRow = 232
    ActiveWindow.ScrollRow = 235
    ActiveWindow.ScrollRow = 240
    ActiveWindow.ScrollRow = 242
    ActiveWindow.ScrollRow = 245
    ActiveWindow.ScrollRow = 248
    ActiveWindow.ScrollRow = 250
    ActiveWindow.ScrollRow = 253
    ActiveWindow.ScrollRow = 256
    ActiveWindow.ScrollRow = 258
    ActiveWindow.ScrollRow = 262
    ActiveWindow.ScrollRow = 266
    ActiveWindow.ScrollRow = 269
    ActiveWindow.ScrollRow = 271
    ActiveWindow.ScrollRow = 275
    ActiveWindow.ScrollRow = 278
    ActiveWindow.ScrollRow = 282
    ActiveWindow.ScrollRow = 284
    ActiveWindow.ScrollRow = 286
    ActiveWindow.ScrollRow = 288
    ActiveWindow.ScrollRow = 291
    ActiveWindow.ScrollRow = 292
    ActiveWindow.ScrollRow = 294
    ActiveWindow.ScrollRow = 296
    ActiveWindow.ScrollRow = 297
    ActiveWindow.ScrollRow = 298
    ActiveWindow.ScrollRow = 300
    ActiveWindow.ScrollRow = 301
    ActiveWindow.ScrollRow = 303
    ActiveWindow.ScrollRow = 306
    ActiveWindow.ScrollRow = 309
    ActiveWindow.ScrollRow = 310
    ActiveWindow.ScrollRow = 313
    ActiveWindow.ScrollRow = 314
    ActiveWindow.ScrollRow = 316
    ActiveWindow.ScrollRow = 318
    ActiveWindow.ScrollRow = 320
    ActiveWindow.ScrollRow = 322
    ActiveWindow.ScrollRow = 323
    ActiveWindow.ScrollRow = 325
    ActiveWindow.ScrollRow = 326
    ActiveWindow.ScrollRow = 329
    ActiveWindow.ScrollRow = 331
    Range("Y352").Select
    Selection.NumberFormat = "_(* #,##0_);_(* (#,##0);_(* ""-""_);_(@_)"
    Range("A1:Y352").Select
    Range("Y352").Activate
    ActiveWindow.ScrollColumn = 2
    ActiveWindow.ScrollColumn = 3
    ActiveWindow.ScrollColumn = 4
    ActiveWindow.ScrollColumn = 5
    Selection.Cut Destination:=Range("A2:Y353")
    Range("A2:Y353").Select
    Selection.Borders(xlDiagonalDown).LineStyle = xlNone
    Selection.Borders(xlDiagonalUp).LineStyle = xlNone
    With Selection.Borders(xlEdgeLeft)
        .LineStyle = xlContinuous
        .ColorIndex = 0
        .TintAndShade = 0
        .Weight = xlThin
    End With
    With Selection.Borders(xlEdgeTop)
        .LineStyle = xlContinuous
        .ColorIndex = 0
        .TintAndShade = 0
        .Weight = xlThin
    End With
    With Selection.Borders(xlEdgeBottom)
        .LineStyle = xlContinuous
        .ColorIndex = 0
        .TintAndShade = 0
        .Weight = xlThin
    End With
    With Selection.Borders(xlEdgeRight)
        .LineStyle = xlContinuous
        .ColorIndex = 0
        .TintAndShade = 0
        .Weight = xlThin
    End With
    With Selection.Borders(xlInsideVertical)
        .LineStyle = xlContinuous
        .ColorIndex = 0
        .TintAndShade = 0
        .Weight = xlThin
    End With
    With Selection.Borders(xlInsideHorizontal)
        .LineStyle = xlContinuous
        .ColorIndex = 0
        .TintAndShade = 0
        .Weight = xlThin
    End With
    ActiveWindow.ScrollColumn = 2
    ActiveWindow.ScrollColumn = 3
    ActiveWindow.ScrollColumn = 4
    ActiveWindow.ScrollColumn = 3
    ActiveWindow.ScrollColumn = 2
    ActiveWindow.ScrollColumn = 1
    ActiveWindow.ScrollColumn = 2
    ActiveWindow.ScrollColumn = 3
    ActiveWindow.ScrollColumn = 4
    ActiveWindow.ScrollColumn = 3
    ActiveWindow.ScrollColumn = 2
    ActiveWindow.ScrollColumn = 1
    Range("B2").Select
    ActiveWindow.ScrollColumn = 2
    ActiveWindow.ScrollColumn = 3
    ActiveWindow.ScrollColumn = 4
    ActiveWindow.ScrollColumn = 5
    ActiveWindow.ScrollColumn = 4
    ActiveWindow.ScrollColumn = 3
    ActiveWindow.ScrollColumn = 2
    ActiveWindow.ScrollColumn = 1
    ActiveWindow.ScrollColumn = 2
    ActiveWindow.ScrollColumn = 3
    ActiveWindow.ScrollColumn = 4
    ActiveWindow.ScrollColumn = 5
    ActiveWindow.ScrollColumn = 4
    ActiveWindow.ScrollColumn = 3
    ActiveWindow.ScrollColumn = 2
    ActiveWindow.ScrollColumn = 1
    Range("A2").Select
    ActiveCell.FormulaR1C1 = "S/" & Chr(10) & "NO"
    Rows("3:353").Select
    Selection.RowHeight = 18
    Range("F2:W2").Select
    Selection.ColumnWidth = 3.86
    Columns("X:X").EntireColumn.AutoFit
    Columns("Y:Y").EntireColumn.AutoFit
    ActiveWindow.ScrollColumn = 4
    ActiveWindow.ScrollColumn = 3
    ActiveWindow.ScrollColumn = 2
    ActiveWindow.ScrollColumn = 1
    Columns("C:C").EntireColumn.AutoFit
    Range("A1:Y1").Select
    With Selection
        .HorizontalAlignment = xlCenter
        .VerticalAlignment = xlCenter
        .WrapText = False
        .Orientation = 0
        .AddIndent = False
        .IndentLevel = 0
        .ShrinkToFit = False
        .ReadingOrder = xlContext
        .MergeCells = False
    End With
    Selection.Merge
    ActiveCell.FormulaR1C1 = "OJT - MAY - 2025 PAYMENT"
    Range("A1:Y1").Select
    Selection.Font.Bold = True
    Rows("1:1").Select
    Selection.RowHeight = 27
    Selection.Font.Size = 12
    Rows("2:2").Select
    Selection.RowHeight = 64.5
    ActiveWindow.ScrollColumn = 2
    ActiveWindow.ScrollColumn = 1
    Range("B2").Select
    ActiveWindow.ScrollRow = 2
    ActiveWindow.ScrollRow = 5
    ActiveWindow.ScrollRow = 6
    ActiveWindow.ScrollRow = 8
    ActiveWindow.ScrollRow = 9
    ActiveWindow.ScrollRow = 11
    ActiveWindow.ScrollRow = 12
    ActiveWindow.ScrollRow = 14
    ActiveWindow.ScrollRow = 15
    ActiveWindow.ScrollRow = 16
    ActiveWindow.ScrollRow = 17
    ActiveWindow.ScrollRow = 18
    ActiveWindow.ScrollRow = 19
    ActiveWindow.ScrollRow = 20
    ActiveWindow.ScrollRow = 21
    ActiveWindow.ScrollRow = 22
    ActiveWindow.ScrollRow = 23
    ActiveWindow.ScrollRow = 24
    ActiveWindow.ScrollRow = 25
    ActiveWindow.ScrollRow = 27
    ActiveWindow.ScrollRow = 28
    ActiveWindow.ScrollRow = 29
    ActiveWindow.ScrollRow = 30
    ActiveWindow.ScrollRow = 31
    ActiveWindow.ScrollRow = 32
    ActiveWindow.ScrollRow = 33
    ActiveWindow.ScrollRow = 34
    ActiveWindow.ScrollRow = 35
    ActiveWindow.ScrollRow = 36
    ActiveWindow.ScrollRow = 37
    ActiveWindow.ScrollRow = 38
    ActiveWindow.ScrollRow = 39
    ActiveWindow.ScrollRow = 40
    ActiveWindow.ScrollRow = 41
    ActiveWindow.ScrollRow = 43
    ActiveWindow.ScrollRow = 44
    ActiveWindow.ScrollRow = 46
    ActiveWindow.ScrollRow = 47
    ActiveWindow.ScrollRow = 49
    ActiveWindow.ScrollRow = 50
    ActiveWindow.ScrollRow = 52
    ActiveWindow.ScrollRow = 53
    ActiveWindow.ScrollRow = 54
    ActiveWindow.ScrollRow = 55
    ActiveWindow.ScrollRow = 56
    ActiveWindow.ScrollRow = 57
    ActiveWindow.ScrollRow = 58
    ActiveWindow.ScrollRow = 60
    ActiveWindow.ScrollRow = 61
    ActiveWindow.ScrollRow = 63
    ActiveWindow.ScrollRow = 65
    ActiveWindow.ScrollRow = 67
    ActiveWindow.ScrollRow = 70
    ActiveWindow.ScrollRow = 72
    ActiveWindow.ScrollRow = 74
    ActiveWindow.ScrollRow = 78
    ActiveWindow.ScrollRow = 81
    ActiveWindow.ScrollRow = 83
    ActiveWindow.ScrollRow = 86
    ActiveWindow.ScrollRow = 88
    ActiveWindow.ScrollRow = 92
    ActiveWindow.ScrollRow = 94
    ActiveWindow.ScrollRow = 97
    ActiveWindow.ScrollRow = 101
    ActiveWindow.ScrollRow = 104
    ActiveWindow.ScrollRow = 107
    ActiveWindow.ScrollRow = 110
    ActiveWindow.ScrollRow = 114
    ActiveWindow.ScrollRow = 118
    ActiveWindow.ScrollRow = 123
    ActiveWindow.ScrollRow = 128
    ActiveWindow.ScrollRow = 132
    ActiveWindow.ScrollRow = 150
    ActiveWindow.ScrollRow = 155
    ActiveWindow.ScrollRow = 161
    ActiveWindow.ScrollRow = 166
    ActiveWindow.ScrollRow = 173
    ActiveWindow.ScrollRow = 179
    ActiveWindow.ScrollRow = 187
    ActiveWindow.ScrollRow = 195
    ActiveWindow.ScrollRow = 203
    ActiveWindow.ScrollRow = 212
    ActiveWindow.ScrollRow = 228
    ActiveWindow.ScrollRow = 236
    ActiveWindow.ScrollRow = 251
    ActiveWindow.ScrollRow = 257
    ActiveWindow.ScrollRow = 262
    ActiveWindow.ScrollRow = 267
    ActiveWindow.ScrollRow = 271
    ActiveWindow.ScrollRow = 275
    ActiveWindow.ScrollRow = 277
    ActiveWindow.ScrollRow = 279
    ActiveWindow.ScrollRow = 281
    ActiveWindow.ScrollRow = 283
    ActiveWindow.ScrollRow = 285
    ActiveWindow.ScrollRow = 287
    ActiveWindow.ScrollRow = 288
    ActiveWindow.ScrollRow = 289
    ActiveWindow.ScrollRow = 291
    ActiveWindow.ScrollRow = 292
    ActiveWindow.ScrollRow = 293
    ActiveWindow.ScrollRow = 295
    ActiveWindow.ScrollRow = 296
    ActiveWindow.ScrollRow = 297
    ActiveWindow.ScrollRow = 299
    ActiveWindow.ScrollRow = 301
    ActiveWindow.ScrollRow = 302
    ActiveWindow.ScrollRow = 303
    ActiveWindow.ScrollRow = 305
    ActiveWindow.ScrollRow = 306
    ActiveWindow.ScrollRow = 307
    ActiveWindow.ScrollRow = 308
    ActiveWindow.ScrollRow = 309
    ActiveWindow.ScrollRow = 310
    ActiveWindow.ScrollRow = 311
    ActiveWindow.ScrollRow = 313
    ActiveWindow.ScrollRow = 314
    ActiveWindow.ScrollRow = 315
    ActiveWindow.ScrollRow = 316
    ActiveWindow.ScrollRow = 317
    ActiveWindow.ScrollRow = 318
    ActiveWindow.ScrollRow = 319
    ActiveWindow.ScrollRow = 321
    ActiveWindow.ScrollRow = 322
    ActiveWindow.ScrollRow = 323
    ActiveWindow.ScrollRow = 324
    ActiveWindow.ScrollRow = 325
    ActiveWindow.ScrollRow = 326
    ActiveWindow.ScrollRow = 327
    ActiveWindow.ScrollRow = 328
    ActiveWindow.ScrollRow = 329
    ActiveWindow.ScrollRow = 330
    ActiveWindow.ScrollRow = 331
    ActiveWindow.ScrollRow = 332
    ActiveWindow.ScrollRow = 333
    ActiveWindow.ScrollRow = 334
    ActiveWindow.ScrollRow = 335
    ActiveWindow.ScrollRow = 336
    Range("A353:X353").Select
    Range("X353").Activate
    With Selection
        .HorizontalAlignment = xlCenter
        .VerticalAlignment = xlCenter
        .WrapText = False
        .Orientation = 0
        .AddIndent = False
        .IndentLevel = 0
        .ShrinkToFit = False
        .ReadingOrder = xlContext
        .MergeCells = False
    End With
    Selection.Merge
    ActiveCell.FormulaR1C1 = "Total"
    Rows("353:353").Select
    Selection.Font.Bold = True
    ActiveWindow.ScrollColumn = 2
    Columns("Y:Y").Select
    Range("Y336").Activate
    Columns("Y:Y").EntireColumn.AutoFit
    ActiveWindow.ScrollColumn = 3
    ActiveWindow.ScrollRow = 333
    ActiveWindow.ScrollRow = 331
    ActiveWindow.ScrollRow = 328
    ActiveWindow.ScrollRow = 326
    ActiveWindow.ScrollRow = 322
    ActiveWindow.ScrollRow = 318
    ActiveWindow.ScrollRow = 308
    ActiveWindow.ScrollRow = 301
    ActiveWindow.ScrollRow = 296
    ActiveWindow.ScrollRow = 287
    ActiveWindow.ScrollRow = 278
    ActiveWindow.ScrollRow = 270
    ActiveWindow.ScrollRow = 251
    ActiveWindow.ScrollRow = 240
    ActiveWindow.ScrollRow = 231
    ActiveWindow.ScrollRow = 220
    ActiveWindow.ScrollRow = 209
    ActiveWindow.ScrollRow = 198
    ActiveWindow.ScrollRow = 187
    ActiveWindow.ScrollRow = 175
    ActiveWindow.ScrollRow = 163
    ActiveWindow.ScrollRow = 151
    ActiveWindow.ScrollRow = 138
    ActiveWindow.ScrollRow = 124
    ActiveWindow.ScrollRow = 111
    ActiveWindow.ScrollRow = 97
    ActiveWindow.ScrollRow = 84
    ActiveWindow.ScrollRow = 70
    ActiveWindow.ScrollRow = 59
    ActiveWindow.ScrollRow = 47
    ActiveWindow.ScrollRow = 36
    ActiveWindow.ScrollRow = 25
    ActiveWindow.ScrollRow = 15
    ActiveWindow.ScrollRow = 6
    ActiveWindow.ScrollRow = 1
    ActiveWindow.ScrollColumn = 2
    ActiveWindow.ScrollColumn = 1
    Range("A2").Select
    Columns("E:E").EntireColumn.AutoFit
    Columns("D:D").EntireColumn.AutoFit
    Columns("C:C").EntireColumn.AutoFit
    Columns("B:B").EntireColumn.AutoFit
    Columns("A:A").EntireColumn.AutoFit
    Range("A2").Select
End Sub
