# Session Timeout Implementation

This document describes the comprehensive session timeout implementation for the MPMA OJT Management System.

## Overview

The system now includes automatic session management with the following features:
- 1-hour session duration
- 5-minute warning before expiration
- Automatic logout when session expires
- Real-time session status display
- Global error handling for expired tokens

## Components

### 1. Session Context (`src/contexts/SessionContext.tsx`)

**Purpose**: Centralized session state management

**Key Features**:
- Tracks session start time and remaining time
- Monitors session expiration
- Provides session management functions
- Shows warning 5 minutes before expiration
- Automatically logs out when session expires

**Configuration**:
```typescript
const SESSION_DURATION = 60 * 60 * 1000; // 1 hour
const WARNING_TIME = 5 * 60 * 1000; // Show warning 5 minutes before expiration
const CHECK_INTERVAL = 30 * 1000; // Check every 30 seconds
```

**Note**: Backend token expiration is set to 1 hour to match frontend session duration.

### 2. Session Timeout Warning (`src/Components/SessionTimeoutWarning.tsx`)

**Purpose**: Modal dialog that warns users about session expiration

**Features**:
- Countdown timer showing exact time remaining
- Progress bar with color coding (green → yellow → red)
- Options to dismiss warning or logout immediately
- Prevents accidental closure (backdrop static)

### 3. Axios Response Interceptor (`src/api.ts`)

**Purpose**: Global handling of 401 responses

**Functionality**:
- Automatically detects session expiration (401 responses)
- Clears stored tokens
- Shows session expired message
- Redirects to login page

### 4. Header Session Indicator (`src/layout/header/Header.tsx`)

**Purpose**: Real-time session status display

**Features**:
- Shows remaining session time
- Color-coded status (green/yellow/red)
- Updates every 30 seconds

## Backend Updates

### 1. Enhanced Error Handling (`src/Core/AuthWrapper.js`)

**Improvements**:
- Specific handling for JWT expiration errors
- Returns "SESSION_EXPIRED" message for expired tokens
- Consistent 401 responses

### 2. Auth Controller Updates (`src/Controllers/AuthController.js`)

**Enhancements**:
- Better token validation
- Specific error messages for different failure types
- Proper handling of missing tokens

## User Experience Flow

### Normal Operation
1. User logs in → Session timer starts (1 hour)
2. Header shows remaining time with green indicator
3. User continues working normally

### Warning Phase (5 minutes remaining)
1. Session warning modal appears
2. Countdown shows exact time remaining
3. Progress bar turns yellow, then red
4. User can:
   - Dismiss warning (continues countdown)
   - Logout immediately

### Automatic Expiration
1. When timer reaches zero:
   - User is automatically logged out
   - All tokens are cleared
   - "Session Expired" message is shown
   - User is redirected to login page

### API Call Failures
1. Any API call with expired token:
   - Returns 401 response
   - Interceptor catches the error
   - User is logged out automatically
   - Redirected to login with message

## Testing

### Test Page
Access `/OJT/session-test` to test session functionality:
- View real-time session status
- Test API calls
- Force logout

### Quick Testing Setup
For faster testing, you can temporarily modify constants in `SessionContext.tsx`:
```typescript
const SESSION_DURATION = 2 * 60 * 1000; // 2 minutes for testing
const WARNING_TIME = 30 * 1000; // 30 seconds warning
```
Remember to also update the backend `TOKEN_EXP_TIME` to match when testing.

## Configuration Options

### Session Duration
Modify `SESSION_DURATION` in `SessionContext.tsx` to change session length.

### Warning Time
Modify `WARNING_TIME` to change when the warning appears.

### Check Interval
Modify `CHECK_INTERVAL` to change how often the system checks session status.

### Backend Token Expiration
Modify `TOKEN_EXP_TIME` in `AuthController.js` to match frontend session duration.

## Security Features

1. **Automatic Cleanup**: All tokens are cleared on expiration
2. **Global Protection**: All API calls are protected by the interceptor
3. **No Silent Failures**: Users are always notified of session expiration
4. **Consistent Behavior**: Same experience across all pages and components

## Browser Compatibility

- Uses localStorage for token storage
- Compatible with all modern browsers
- Handles browser refresh/navigation properly
- Works with multiple tabs (shared session state)

## Troubleshooting

### Common Issues

1. **Session not starting**: Check if token is properly set in localStorage
2. **Warning not showing**: Verify SessionProvider wraps the entire app
3. **API calls still working after expiration**: Check backend token validation
4. **Timer not updating**: Ensure SessionContext is properly imported

### Debug Information

- Check browser console for session-related logs
- Use the test page to monitor session state
- Verify token presence in localStorage
- Check network tab for 401 responses

## Future Enhancements

Potential improvements:
1. Server-side session validation
2. Multiple session management
3. Session analytics and monitoring
4. Configurable warning times per user role
5. Activity-based session monitoring
