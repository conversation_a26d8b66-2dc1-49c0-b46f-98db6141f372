import {
  AcessLevel,
  Department,
  Interview,
  Schedule,
  Section,
  Trainee,
  User,
} from "../models/index.js";
import { sequalize } from "./sequlize.js";

export const syncDb = async () => {
  try {
    /*
    Department.sync({ alter: true });
    Section.sync({ alter: true }); */
    //AcessLevel.sync({ force: true });
    //User.sync({ force: true });
    /* await User.create({
      name: "MPMA -SUPER ADMIN",
      type: "SUPERADMIN",
      username: "mpma@superadmin",
      password: "@WSXxsw2",
    }); */
    //Interview.sync({ force: true });
    //Schedule.sync({ alter: true });
    //Trainee.sync({ alter: true });
  } catch (error) {
    console.log(error);
    console.log("sync failed");
  }
};
