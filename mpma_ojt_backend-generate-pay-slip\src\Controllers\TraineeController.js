import { format } from "mysql2";
import { promisePool } from "../DB/Database.js";
import { date, record, z } from "zod";
import { ErrorWithStatus } from "../ErrorWithStatus.js";
import moment from "moment";
import { response } from "express";

//@desc get all trainees
//route GET api/trainee/

export const store = async (req, res, next) => {
  const schema = z.object({
    ATT_NO: z.coerce
      .number()
      .gt(0)
      .nonnegative()
      .refine((att_no) => {
        //todo
        //need to check if the att no is not used in the database
        return true;
      }),
    REG_NO: z
      .string()
      .min(1)
      .refine(() => {
        //todo
        //need to check if the regno is not used in the database
        return true;
      }),
    name: z.string().min(1),
    NIC_NO: z
      .string()
      .min(1)
      .refine((nic) => {
        //todo
        //need to check if the nic is not used
        return true;
      }),
    TEL_NO: z.string().regex(/^\d{9,10}$/),
    program: z.coerce
      .number()
      .gt(0)
      .refine((program) => {
        //todo
        //need to check the programs exists in the database
        return true;
      }),
    institute: z.coerce.number().refine((institute) => {
      //todo
      //need to check the institute exists in the database
      return true;
    }),
    Jstart_date: z.string().date(),
    Jend_date: z.string().date(),
    period: z.coerce.number().refine((period) => {
      //todo
      //need to check the period exists in the database
      return true;
    }),
  });

  const connection = await promisePool.getConnection(); // Get a connection from the pool

  try {
    const data = schema.parse(req.body); // Validate and parse the incoming data
    //return res.status(200).json(data);
    // Start the transaction
    await connection.beginTransaction();
    try {
      // Insert trainee data into `user_data`
      const [detailResults] = await connection.query(
        `INSERT INTO trainee
        (name, ATT_NO, REG_NO, NIC_NO, contact_no, status, institute_id, training_program_id)
        VALUES (?,?,?,?,?,?,?,?)`,
        [
          data.name,
          data.ATT_NO,
          data.REG_NO,
          data.NIC_NO,
          data.TEL_NO,
          true,
          data.institute,
          data.program,
        ]
      );

      const journeyResults = await connection.query(
        "INSERT INTO `journey`(`trainee_id`,`start_date`, `end_date`, `period_id`) VALUES (?,?,?,?)",
        [detailResults.insertId, data.Jstart_date, data.Jend_date, data.period]
      );

      // Commit the transaction
      await connection.commit();

      // Send success response
      return res.status(201).json({
        message: "Trainee data inserted successfully",
        queryResults: [detailResults, journeyResults],
      });
    } catch (error) {
      // Rollback if there's an error
      await connection.rollback();
      throw error;
    }
  } catch (exception) {
    // Handle validation or transaction errors
    const error = new Error(exception.message || "Data processing failed");
    error.status = 400;
    next(error);
  } finally {
    // Ensure the connection is released back to the pool
    connection.release();
  }
};

/**
 *return the trainee when provided the trainee id
 * @param {number} id trainee_id
 * @returns {Promise<Object}
 */
export const getTrainee = async (id) => {
  try {
    const [trainees] = await promisePool.query(
      `
      SELECT 
      trainee.id,
      trainee.name,
      trainee.ATT_NO,
      trainee.REG_NO,
      trainee.NIC_NO,
      trainee.contact_no,
      trainee.status,
      journey.start_date,
      journey.end_date,
      periods.id as training_period_id,
      trainee.institute_id,
      trainee.training_program_id 
      FROM
        trainee,journey,training_program,institute,periods
      WHERE
        trainee.id=journey.trainee_id AND
        trainee.training_program_id=training_program.id AND 
        trainee.institute_id=institute.id and
        journey.period_id=periods.id and 
        trainee.id=?
      `,
      [id]
    );
    if (trainees.length > 0) {
      trainees.forEach((trainee) => {
        const start_date = moment(trainee.start_date).format("YYYY-MM-DD");
        const end_date = moment(trainee.end_date).format("YYYY-MM-DD");
      });
      trainees[0].start_date = moment(trainees[0].start_date).format(
        "YYYY-MM-DD"
      );
      trainees[0].end_date = moment(trainees[0].end_date).format("YYYY-MM-DD");

      return trainees[0];
    } else {
      return null;
    }
    const start_date = moment(trainee.start_date).format("YYYY-MM-DD");
    const end_date = moment(trainee.end_date).format("YYYY-MM-DD");
  } catch (error) {
    throw error;
  }
};

export const index = async (req, res, next) => {
  try {
    //Execute the initial query to get all trainees
    const [trainees] = await promisePool.query(`
      	SELECT 
      trainee.id,
      trainee.name,
      trainee.ATT_NO,
      trainee.REG_NO,
      trainee.NIC_NO,
      trainee.contact_no,
      trainee.status,
      journey.start_date,
      journey.end_date,
      periods.name as training_period,
      institute.name AS institute,
      training_program.name AS program
      FROM
        trainee,journey,training_program,institute,periods
      WHERE
        trainee.status = 1 AND
        trainee.id=journey.trainee_id AND
        trainee.training_program_id=training_program.id AND 
        trainee.institute_id=institute.id and
        journey.period_id=periods.id
        order by trainee.id desc
      `);
    // Use Promise.all to handle all async operations concurrently
    const traineeDetails = await Promise.all(
      trainees.map(async (trainee) => {
        const start_date = moment(trainee.start_date).format("YYYY-MM-DD");
        const end_date = moment(trainee.end_date).format("YYYY-MM-DD");
        // Query for each trainee's schedule based on their NIC_NO
        const [schedules] = await promisePool.query(
          `SELECT 
            department.name,
            schedule.start_date,
            schedule.end_date
            FROM
              schedule,department
            WHERE
              schedule.department_id=department.id and
              schedule.trainee_id=?;`,
          [trainee.id] // Use parameterized query to prevent SQL injection
        );

        if (schedules.length > 0) {
          trainee.schedules = schedules;
        }
        // Assign the schedule to the trainee object
        return {
          ...trainee,
          start_date,
          end_date,
        };
      })
    );
    // Send the resolved traineeDetails array as a JSON response
    return res.status(200).json(traineeDetails);
  } catch (e) {
    // Handle and log errors
    const error = new Error(e.code || "unknown error occurred");
    next(error); // Pass error to the next middleware
  }
};

export const show = async (req, res, next) => {
  const schema = z.object({
    traineeId: z.coerce.number(),
  });
  try {
    // Fetch trainee details
    const data = schema.parse(req.params);
    const trainee = await getTrainee(data.traineeId);
    if (!trainee) {
      throw new ErrorWithStatus(404, "no trainee found with the nic");
    }
    return res.status(200).json(trainee);
  } catch (e) {
    next(e);
  }
};

export const getTraineeByATT_NO = async (ATT_NO) => {
  const [trainees] = await promisePool.query(
    `SELECT trainee.id,trainee.name,trainee.REG_NO,journey.end_date FROM trainee,journey where ATT_NO=${ATT_NO} and trainee.id=journey.trainee_id;`
  );
  /* if (trainees.length == 0) {
    console.log(ATT_NO, "no record found");
  } */
  return trainees[0];
};

export const checkRegNoViolation = async (req, res, next) => {
  const schema = z.object({
    traineeId: z.coerce.number(),
  });
  try {
    const data = schema.parse(req.query); //the query is used because the validation doesent request a resource related to trainee
    const trainee = await getTrainee(data.traineeId);
    if (!trainee) {
      throw new ErrorWithStatus(404, "no trainee found with the trainee id");
    }
    const regPattern = trainee.REG_NO.split("/");
    let searchPattern;
    if (regPattern[0] == "CINEC") {
      searchPattern = "CINEC%";
    } else if (regPattern[1] == "NAITA") {
      searchPattern = "%NAITA%";
    } else if (regPattern[0] == "SMTI") {
      searchPattern = "SMTI%";
    } else {
      searchPattern = `${regPattern[0]}%`;
    }
    const [lastTraineeWithRegPattern] = await promisePool.query(
      `SELECT * FROM trainee where REG_NO LIKE '${searchPattern}' and id>${trainee.id} order by id desc LIMIT 1  ;`
    );
    let response;
    if (lastTraineeWithRegPattern.length > 0) {
      response = {
        exists: true,
        trainee: lastTraineeWithRegPattern[0],
      };
    } else {
      response = {
        exists: false,
      };
    }

    return res.status(200).json(response);
  } catch (error) {
    next(error);
  }
};

export const update = async (req, res, next) => {
  const schema = z.object({
    trainee_id: z.coerce.number(),
    ATT_NO: z.coerce
      .number()
      .gt(0)
      .nonnegative()
      .refine((att_no) => {
        //todo
        //need to check if the att no is not used in the database
        return true;
      }),
    REG_NO: z
      .string()
      .min(1)
      .refine(() => {
        //todo
        //need to check if the regno is not used in the database
        return true;
      }),
    name: z.string().min(1),
    NIC_NO: z
      .string()
      .min(1)
      .refine((nic) => {
        //todo
        //need to check if the nic is not used
        return true;
      }),
    TEL_NO: z.string().regex(/^\d{9,10}$/),
    program: z.coerce
      .number()
      .gt(0)
      .refine((program) => {
        //todo
        //need to check the programs exists in the database
        return true;
      }),
    institute: z.coerce.number().refine((institute) => {
      //todo
      //need to check the institute exists in the database
      return true;
    }),
  });

  try {
    const data = schema.parse({ ...req.body, trainee_id: req.params.id });
    const [result] = await promisePool.query(
      "update trainee set name =?,ATT_NO=?,REG_NO=?,NIC_NO=?,contact_no=?,institute_id=?,training_program_id=? where id=?",
      [
        data.name,
        data.ATT_NO,
        data.REG_NO,
        data.NIC_NO,
        data.TEL_NO,
        data.institute,
        data.program,
        data.trainee_id,
      ]
    );
    return res.status(200).json(result);
  } catch (error) {
    next(error);
  }
};

export const getAvailableTrainingPeriods = async (req, res, next) => {
  try {
    // Fetch trainee details
    const [periods] = await promisePool.query(
      "SELECT distinct TRAINING_PERIOD FROM user_data;"
    );
    return res.json(periods);
  } catch (e) {
    // Handle and log errors
    next(e);
  }
};

export const getTrainingPrograms = async (req, res, next) => {
  try {
    // Fetch  programs
    const [programs] = await promisePool.query(
      "select distinct TRAINING_PROGRAMME from user_data;"
    );
    return res.json(programs);
  } catch (e) {
    // Handle and log errors
    next(e);
  }
};
export const getInstitutes = async (req, res, next) => {};

export const filterData = async (req, res, next) => {
  //return res.status(200).json(req.query);
  const data = req.query;
  let conditions = [];

  if (data.includeInactiveTrainees == "false") {
    conditions.push("trainee.status = 1 ");
  } else {
    conditions.push("(trainee.status = 1 or trainee.status=0 )");
  }

  if (data.programmes != null) {
    conditions.push(
      `  trainee.training_program_id IN (${data.programmes
        .map((programme) => {
          return `'${programme}'`;
        })
        .join(" , ")}) `
    );
  }

  if (data.institutes != null) {
    conditions.push(
      ` trainee.institute_id IN (${data.institutes
        .map((institute) => {
          return `'${institute}'`;
        })
        .join(" , ")}) `
    );
  }

  if (data.departments != null) {
    conditions.push(
      ` schedule.department_id IN (${data.departments
        .map((department) => {
          return `'${department}'`;
        })
        .join(" , ")}) `
    );
  }

  if (data.start_date != "" && data.start_date != null) {
    conditions.push(` journey.start_Date>='${data.start_date}' `);
  }

  if (data.end_date != "" && data.end_date != null) {
    conditions.push(` journey.start_Date<='${data.end_date}' `);
  }

  let query;

  if (data.departments == null) {
    query = `
    SELECT
    trainee.id,
    trainee.ATT_NO,
    trainee.REG_NO,
    trainee.name,
    trainee.NIC_NO,
    trainee.contact_no,
    training_program.name as program,
    institute.name as institute,
    periods.name as training_period,
    journey.start_date ,
    journey.end_date 
    FROM trainee
    JOIN training_program on trainee.training_program_id=training_program.id
    JOIN institute on trainee.institute_id=institute.id
    JOIN journey ON trainee.id = journey.trainee_id
    JOIN periods ON journey.period_id=periods.id
    WHERE ${conditions.join("AND")} 
      group by  
      trainee.id,
      trainee.ATT_NO,
      trainee.REG_NO,
      trainee.name,
      trainee.NIC_NO,
      trainee.contact_no,
      training_program.name,
      institute.name,
      periods.name,
      journey.start_date,
      journey.end_date
      order by trainee.id desc
    `;
  } else {
    query = `
    SELECT
    trainee.id,
    trainee.ATT_NO,
    trainee.REG_NO,
    trainee.name,
    trainee.NIC_NO,
    trainee.contact_no,
    training_program.name as program,
    institute.name as institute,
    periods.name as training_period,
    journey.start_date ,
    journey.end_date 
    FROM trainee
    JOIN training_program on trainee.training_program_id=training_program.id
    JOIN institute on trainee.institute_id=institute.id
    JOIN journey ON trainee.id = journey.trainee_id
    JOIN periods ON journey.period_id=periods.id
    JOIN schedule ON schedule.trainee_id = trainee.id
    WHERE ${conditions.join("AND")} 
    group by  
    trainee.id,
    trainee.ATT_NO,
    trainee.REG_NO,
    trainee.name,
    trainee.NIC_NO,
    trainee.contact_no,
    training_program.name,
    institute.name,
    periods.name,
    journey.start_date,
    journey.end_date
    order by trainee.id desc
  `;
  }

  console.log(query);
  const [trainees] = await promisePool.query(query);

  const traineeDetails = await Promise.all(
    trainees.map(async (trainee) => {
      // Query for each trainee's schedule based on their NIC_NO
      const [schedules] = await promisePool.query(
        `SELECT * FROM schedule
        WHERE trainee_id = ?`,
        [trainee.id] // Use parameterized query to prevent SQL injection
      );
      trainee.schedules = schedules;
      // Assign the schedule to the trainee object

      return trainee;
    })
  );

  return res.status(200).json(traineeDetails);
};

/**
 * checks if the nic has another record on the database
 * @param {*} req
 * @param {*} res
 * @param {*} next
 */
export const checkEligibility = async (req, res, next) => {
  const schema = z.object({
    NIC_NO: z.string(),
  });
  try {
    const data = schema.parse(req.params);
    const [records] = await promisePool.query(
      "select * from trainee where NIC_NO=?",
      [data.NIC_NO]
    );

    if (records.length > 0) {
      return res.status(200).json({
        exists: true,
        record: records[0],
      });
    }
    return res.status(200).json({ exists: false, nic: data.NIC_NO });
  } catch (e) {
    next(e);
  }
};

/**
 * recreate the logic as needed
 * generates the regitration No with the training program and university
 * genrates the attendence number which must be generated incuding the year
 * @param {*} req
 * @param {*} res
 * @param {*} next
 */

export const generateIndexNos = async (req, res, next) => {
  const schema = z.object({
    institute: z.coerce.number().gt(0),
    program: z.coerce.number().gt(0),
    code_generation_pattern: z.enum(["naita", "cinec", "normal", "smti"]),
  });

  try {
    let ATT_NO;
    let REG_NO;
    const date = new Date();
    const year = date.getUTCFullYear();
    const data = schema.parse(req.query);
    //return res.status(200).json(data);
    //get the current year
    //return res.status(200).json(data);
    //get the training programs using the id recieved
    const [[training_program]] = await promisePool.query(
      "select * from training_program where id=?",
      [data.program]
    );

    //get the institute using the id recieved
    const [[institute]] = await promisePool.query(
      "select * from institute where id=?",
      [data.institute]
    );

    /* 
      ATTENDENCE Number Generation
      eg:- 9240548 or 8240548
      9/8 - paid/unpaid
      24 - year
      0548 - index
    */

    //get the last inserted trainee details in the current year

    const [[last_trainee]] = await promisePool.query(
      `
      select 
      trainee.id,
      trainee.ATT_NO,
      trainee.name,
      journey.start_date,
      journey.end_date
      from
      trainee,journey
      where
        trainee.id=journey.trainee_id and
        year(journey.start_date)=?
      order by id desc limit 1 `,
      [year]
    );

    if (last_trainee) {
      if (institute.is_government) {
        //paid students are assingned with number 9 at the begining of attendence no.
        ATT_NO = `9${year % 100}${((last_trainee.ATT_NO % 10000) + 1)
          .toString()
          .padStart(4, 0)}`;
      } else {
        //unpaid students are assigned with the number 8 at the begining of the attendence number.
        ATT_NO = `8${year % 100}${((last_trainee.ATT_NO % 10000) + 1)
          .toString()
          .padStart(4, 0)}`;
      }
    } else {
      //if there is no trainne record for the current year.
      if (institute.is_government) {
        ATT_NO = `9${year % 100}0001`;
      } else {
        ATT_NO = `8${year % 100}0001`;
      }
    }

    /*
    
    registration number generation
    not all the trainees have the institute code in the registration number 
    only who has been pre interviewed include the institute code in the registration number
    eg:- 24/NAITA/GF/001
    all the other trainees regardless of the institute will not have the institute code in the registration number
    eg:-CERT/2024/01

    */

    if (data.code_generation_pattern == "cinec") {
      //if the institute code is to be included in the registration number
      if (institute.code) {
        const [[last_trainee]] = await promisePool.query(`
          select REG_NO from trainee where REG_NO like '${institute.code}/${year}/%' order by id desc limit 1
          `);
        if (last_trainee) {
          const lastIndex = parseInt(last_trainee.REG_NO.match(/\d+$/)[0]);
          REG_NO = `${institute.code}/${year}/${lastIndex + 1}`;
        } else {
          //if there is no trainee
          REG_NO = `${institute.code}/${year}/1`;
        }
      } else {
        throw new ErrorWithStatus(400, "Institute Doesen't Have a code");
      }
    } else if (data.code_generation_pattern == "normal") {
      //if the program code is to be included in the registration number
      if (training_program.code) {
        const [[last_trainee]] = await promisePool.query(
          `
          select REG_NO from trainee where REG_NO like '${training_program.code}/${year}/%' order by id desc limit 1; 
          `
        );
        if (last_trainee) {
          const lastIndex = parseInt(last_trainee.REG_NO.match(/\d+$/)[0]);
          REG_NO = `${training_program.code}/${year}/${lastIndex + 1}`;
        } else {
          //if there is no trainee
          REG_NO = `${training_program.code}/${year}/1`;
        }
      } else {
        throw new ErrorWithStatus(400, "Program Doesen't Have a code");
      }
    } else if (data.code_generation_pattern == "naita") {
      //if the program code and institute code needs to to be inserted in the registration number

      if (training_program.special_code) {
        if (institute.code) {
          const [[last_trainee]] = await promisePool.query(
            `
            select REG_NO from trainee where REG_NO like 
            '${year % 100}/${institute.code}/%' order by id desc limit 1; 
            `
          );
          if (last_trainee) {
            const lastIndex = parseInt(last_trainee.REG_NO.match(/\d+$/)[0]);
            REG_NO = `${year % 100}/${institute.code}/${
              training_program.special_code
            }/${lastIndex + 1}`;
          } else {
            //if there is no trainesse
            REG_NO = `${year % 100}/${institute.code}/${
              training_program.special_code
            }/1`;
          }
        } else {
          throw new ErrorWithStatus(400, "institute Doesn't Have a code");
        }
      } else {
        throw new ErrorWithStatus(
          400,
          "Program doesent have a special program code"
        );
      }
    } else if (data.code_generation_pattern == "smti") {
      if (institute.code) {
        const [[last_trainee]] = await promisePool.query(`
          select REG_NO from trainee where REG_NO like '${institute.code}/${year}/%' order by id desc limit 1
          `);
        if (last_trainee) {
          const lastIndex = parseInt(last_trainee.REG_NO.match(/\d+$/)[0]);
          REG_NO = `${institute.code}/${year}/${lastIndex + 1}`;
        } else {
          //if there is no trainee
          REG_NO = `${institute.code}/${year}/1`;
        }
      } else {
        throw new ErrorWithStatus(400, "Institute Doesen't Have a code");
      }
    }
    return res.status(200).json({
      ATT_NO: ATT_NO,
      REG_NO: REG_NO,
    });
  } catch (e) {
    next(e);
  }

  //get the last inserted attendence number

  //create the new attendence number

  //get the program and institute

  //get the count of trainees from the institute for this year

  //create the new registration number
};

export const getInterview = async (req, res, next) => {
  const schema = z.object({
    traineeId: z.coerce.number(),
  });
  try {
    const data = schema.parse(req.params);
    const [interviews] = await promisePool.query(
      `
      SELECT 
        interviews.*, 
        trainee.id AS trainee_id 
      FROM 
        interviews 
      JOIN 
        trainee 
        ON trainee.NIC_NO = interviews.NIC 
      WHERE 
        trainee.id = ?;
    `,
      [data.traineeId]
    );
    return res.status(200).json(interviews);
  } catch (error) {
    next(error);
  }
};

export const deleteInterview = async (req, res, next) => {
  const schema = z.object({
    traineeId: z.coerce.string(),
  });
  try {
    const data = schema.parse(req.params);
    const result = await promisePool.query(`
      delete 
        interviews
      from
        interviews
      inner join
        trainee 
          ON
        trainee.NIC_NO COLLATE utf8mb4_0900_ai_ci = interviews.NIC
      where
        trainee.id=${data.traineeId};`);
    console.log(result);
    return res.status(204).send();
  } catch (error) {
    next(error);
  }
};

export const getTraineeDetails = async (req, res, next) => {
  try {
    const [alltraineeDetails] = await promisePool.query(
      `
      SELECT 
        SUBSTRING_INDEX(REG_NO, '/', 2) AS cert_year, 
        institute.is_government, 
        COUNT(trainee.ATT_NO) AS total_trainees
      FROM trainee 
      JOIN institute ON trainee.institute_id = institute.id
      GROUP BY cert_year, institute.is_government;
      `
    );

    const [activetraineeDetails] = await promisePool.query(
      `
      SELECT 
        SUBSTRING_INDEX(REG_NO, '/', 2) AS cert_year, 
        institute.is_government, 
        COUNT(trainee.ATT_NO) AS total_trainees
      FROM trainee 
      JOIN institute ON trainee.institute_id = institute.id
      WHERE trainee.status = 1
      GROUP BY cert_year, institute.is_government;
      `
    );

    return res.status(200).json({
      all: alltraineeDetails,
      active: activetraineeDetails,
    });
  } catch (error) {
    next(error);
  }
};
