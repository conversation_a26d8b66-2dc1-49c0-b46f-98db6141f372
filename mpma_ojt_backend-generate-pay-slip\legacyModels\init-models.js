var DataTypes = require("sequelize").DataTypes;
var _attendences = require("./attendences");
var _bank_details = require("./bank_details");
var _department = require("./department");
var _holidays = require("./holidays");
var _institute = require("./institute");
var _journey = require("./journey");
var _periods = require("./periods");
var _schedule = require("./schedule");
var _section = require("./section");
var _trainee = require("./trainee");
var _training_program = require("./training_program");

function initModels(sequelize) {
  var attendences = _attendences(sequelize, DataTypes);
  var bank_details = _bank_details(sequelize, DataTypes);
  var department = _department(sequelize, DataTypes);
  var holidays = _holidays(sequelize, DataTypes);
  var institute = _institute(sequelize, DataTypes);
  var journey = _journey(sequelize, DataTypes);
  var periods = _periods(sequelize, DataTypes);
  var schedule = _schedule(sequelize, DataTypes);
  var section = _section(sequelize, DataTypes);
  var trainee = _trainee(sequelize, DataTypes);
  var training_program = _training_program(sequelize, DataTypes);

  schedule.belongsTo(department, { as: "department", foreignKey: "department_id"});
  department.hasMany(schedule, { as: "schedules", foreignKey: "department_id"});
  section.belongsTo(department, { as: "department", foreignKey: "department_id"});
  department.hasMany(section, { as: "sections", foreignKey: "department_id"});
  trainee.belongsTo(institute, { as: "institute", foreignKey: "institute_id"});
  institute.hasMany(trainee, { as: "trainees", foreignKey: "institute_id"});
  journey.belongsTo(periods, { as: "period", foreignKey: "period_id"});
  periods.hasMany(journey, { as: "journeys", foreignKey: "period_id"});
  journey.belongsTo(trainee, { as: "trainee", foreignKey: "trainee_id"});
  trainee.hasMany(journey, { as: "journeys", foreignKey: "trainee_id"});
  schedule.belongsTo(trainee, { as: "trainee", foreignKey: "trainee_id"});
  trainee.hasMany(schedule, { as: "schedules", foreignKey: "trainee_id"});
  trainee.belongsTo(training_program, { as: "training_program", foreignKey: "training_program_id"});
  training_program.hasMany(trainee, { as: "trainees", foreignKey: "training_program_id"});

  return {
    attendences,
    bank_details,
    department,
    holidays,
    institute,
    journey,
    periods,
    schedule,
    section,
    trainee,
    training_program,
  };
}
module.exports = initModels;
module.exports.initModels = initModels;
module.exports.default = initModels;
