import { map, z } from "zod";
import { AcessLevel, User } from "../models/index.js";
import e from "cors";
import { ErrorWithStatus } from "../ErrorWithStatus.js";
import { createHash } from "node:crypto";
import { jwtVerify, SignJWT } from "jose";
import permissions from "../Core/Permisions.js";
import { Op } from "sequelize";

const secret = new TextEncoder().encode(process.env.AUTH_SECRET);
const ALOGITHM = "HS256";
const TOKEN_EXP_TIME = "2min";

const createToken = async (user) => {
  try {
    console.log(user);
    console.log("encoded secret - ", secret);
    const jwt = await new SignJWT({ user_id: user.id })
      .setProtectedHeader({ alg: ALOGITHM })
      .setIssuedAt()
      .setIssuer("OJT-backend")
      .setExpirationTime(TOKEN_EXP_TIME)
      .sign(secret);
    console.log(jwt);
    return jwt;
  } catch (error) {
    console.log("failed creating token");
    throw error;
  }
};

export const login = async (req, res, next) => {
  const schema = z.object({
    username: z.string(),
    password: z.string(),
  });
  try {
    const data = schema.parse(req.body);
    console.log(data);
    const user = await User.findOne({
      where: { username: data.username },
    });
    console.log(user);
    if (user) {
      if (user.status == "SUSPENDED") {
        throw new ErrorWithStatus(403, "SUSPENDED");
      }
      const password = createHash("sha256").update(data.password).digest("hex");
      if (user.password != password) {
        throw new ErrorWithStatus(401, "Un-Authorized");
      }
      //need to return a JWT to user for authorization
      const token = await createToken(user);
      return res.status(200).json(token);
    } else {
      throw new ErrorWithStatus(404, "NO_USER");
    }
  } catch (error) {
    next(error);
  }
};

export const index = async (req, res, next) => {
  try {
    const users = await User.findAll({
      where: {
        type: {
          [Op.not]: "SUPERADMIN",
        },
      },
      include: [
        {
          model: AcessLevel,
        },
      ],
    });
    return res.status(200).json(users);
  } catch (error) {
    next(error);
  }
};

const logout = () => {
  //here the token must be added to blocked tokens list
};

export const createUser = async (req, res, next) => {
  const schema = z.object({
    name: z.string(),
    password: z.string().optional(),
    username: z.string(),
    accessLevels: z.array(z.string()).optional(),
  });

  try {
    const data = schema.parse(req.body);
    const password = data.password;
    let user;
    if (data.accessLevels) {
      user = await User.create(
        {
          name: data.name,
          type: "ADMIN",
          username: data.username,
          password: password,
          status: "ACTIVE",
          accessLevels:
            data.accessLevels &&
            data.accessLevels.map((level) => {
              return {
                access: level,
              };
            }),
        },
        { include: [AcessLevel] }
      );
    } else {
      user = await User.create({
        name: data.name,
        type: "ADMIN",
        status: "ACTIVE",
        username: data.username,
        password: password,
      });
    }

    console.log("new user created - ", user);
    return res.status(201).json(user);
  } catch (error) {
    next(error);
  }
};

export const auth = async (req, res, next) => {
  try {
    const token = req.headers["authorization"];
    console.log(token);

    if (!token) {
      throw new ErrorWithStatus(401, "NO_TOKEN");
    }

    const { payload, headers } = await jwtVerify(
      token,
      new TextEncoder().encode(process.env.AUTH_SECRET)
    );
    //check the token is not blocked
    //get the user with the access levels
    const user = await User.findByPk(payload.user_id, {
      include: [AcessLevel],
    });

    if (!user) {
      throw new ErrorWithStatus(401, "USER_NOT_FOUND");
    }

    res.status(200).json(user);
  } catch (error) {
    // Check if it's a JWT expiration error
    if (error.code === 'ERR_JWT_EXPIRED' || error.message?.includes('expired')) {
      next(new ErrorWithStatus(401, "SESSION_EXPIRED"));
    } else if (error instanceof ErrorWithStatus) {
      next(error);
    } else {
      next(new ErrorWithStatus(401, "AUTHENTICATION_FAILED"));
    }
  }
};

export const deleteAccount = async (req, res, next) => {
  const schema = z.object({
    id: z.coerce.number(),
  });
  try {
    const data = schema.parse(req.params);
    const user = User.destroy({
      where: {
        id: data.id,
      },
    });
    res.status(200).json(user);
  } catch (error) {
    next(error);
  }
};

export const suspendAccount = async (req, res, next) => {
  const schema = z.object({
    id: z.coerce.number(),
  });
  try {
    const data = schema.parse(req.params);
    console.log(req.user.dataValues);
    const user = await User.update(
      { status: "SUSPENDED" },
      {
        where: {
          id: data.id,
        },
      }
    );
    res.status(200).json(user);
  } catch (error) {
    next(error);
  }
};

export const activeAccount = async (req, res, next) => {
  const schema = z.object({
    id: z.coerce.number(),
  });
  try {
    const data = schema.parse(req.params);
    const user = await User.update(
      { status: "ACTIVE" },
      {
        where: {
          id: data.id,
        },
      }
    );
    res.status(200).json(user);
  } catch (error) {
    next(error);
  }
};

export const updateUser = async (req, res, next) => {
  const schema = z.object({
    id: z.coerce.number(),
    name: z.string().optional(),
    password: z.string().optional(),
    username: z.string().optional(),
    type: z.string().optional(),
    accessLevels: z.array(z.string()).optional(),
  });
  try {
    const data = schema.parse({ ...req.body, id: req.params.id });
    // Find user
    const user = await User.findByPk(data.id, { include: [AcessLevel] });
    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }
    // Update fields
    if (data.name) user.name = data.name;
    if (data.username) user.username = data.username;
    if (data.password) user.password = data.password;
    if (data.type) user.type = data.type;
    // Update access levels if provided
    if (data.accessLevels) {
      // Remove old access levels
      await AcessLevel.destroy({ where: { userId: user.id } });
      // Add new access levels
      const newLevels = data.accessLevels.map((level) => ({
        access: level,
        userId: user.id,
      }));
      await AcessLevel.bulkCreate(newLevels);
    }
    await user.save();
    // Return updated user
    const updatedUser = await User.findByPk(data.id, {
      attributes: { exclude: ["password"] },
      include: [AcessLevel],
    });
    res.status(200).json(updatedUser);
  } catch (error) {
    next(error);
  }
};

export const changePassword = (req, res, next) => {};

export const getAllAccessLevels = (req, res, next) => {
  try {
    return res.status(200).json({ ...permissions });
  } catch (error) {
    next(error);
  }
};

export const getUserById = async (req, res, next) => {
  const schema = z.object({
    id: z.coerce.number(),
  });
  try {
    const data = schema.parse(req.params);
    const user = await User.findByPk(data.id, {
      attributes: { exclude: ["password", "createdAt", "updatedAt"] },
      include: [AcessLevel],
    });
    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }
    res.status(200).json(user);
  } catch (error) {
    next(error);
  }
};

