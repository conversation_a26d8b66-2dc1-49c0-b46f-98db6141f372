import e from "express";
import {
  AnalyzeAttendenceSheet,
  getAttendeceSummary,
  getAttendenceRecords,
  getDateSummary
} from "../Controllers/AttendenceController.js";
import multer from "multer";
import { Auth } from "../Core/AuthWrapper.js";
import { ATTENDENCE_MODIFY, ATTENDENCE_VIEW } from "../Core/Permisions.js";

import { getTraineeDetails } from "../Controllers/TraineeController.js";
const router = e.Router();

const upload = multer({ dest: "uploads/" });

const payment_slips = multer({
  dest: "slips/",
  fileFilter: (req, file, cb) => {
    if (file.mimetype == "text/plain") {
      cb(null, true);
    } else {
      cb(new Error("Only .txt files are allowed!"), false);
    }
  },
});

router.get("/summary", getAttendeceSummary);

router.post(
  "",
  await <PERSON>th([ATTENDENCE_MODIFY]),
  upload.single("attendence_sheet"),
  AnalyzeAttendenceSheet
);

//router.post("/analyzeslip", payment_slips.single("slip"), analyzeSlip);

router.get("", await Auth([ATTENDENCE_VIEW, ATTENDENCE_MODIFY]), getAttendenceRecords);

router.get("/getDateSummary", getDateSummary);

router.get("/traineeDetails", getTraineeDetails);

export default router;
