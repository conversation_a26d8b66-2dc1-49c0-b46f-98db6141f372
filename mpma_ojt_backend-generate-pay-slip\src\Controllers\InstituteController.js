import { z } from "zod";
import { promisePool } from "../DB/Database.js";

const schema = z.object({
  name: z.string().min(2),
  government: z.boolean(),
});

export const store = async (req, res, next) => {
  try {
    const data = schema.parse(req.body);
    const [result] = await promisePool.query(
      "insert into institute(name,is_government) values (?,?)",
      [data.name, data.government]
    );
    return res.status(201).json(result);
  } catch (e) {
    next(e);
  }
};

export const index = async (req, res, next) => {
  try {
    const [institutes] = await promisePool.query("select * from institute");
    return res.status(200).json(institutes);
  } catch (error) {
    next(error);
  }
};
