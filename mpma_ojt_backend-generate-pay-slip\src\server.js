import express from "express";
import trainees from "./routes/Trainees.js";
import depatments from "./routes/Deparments.js";
import programs from "./routes/Programs.js";
import institutes from "./routes/Institutes.js";
import attendence from "./routes/Attendence.js";
import periods from "./routes/Periods.js";
import holidays from "./routes/Calender.js";
import auth from "./routes/Auth.js";
import test from "./routes/Test.js";
import interview from "./routes/Interview.js";
import payments from "./routes/Payment.js";
import path from "path";
import { fileURLToPath } from "url";
import errorHandler from "./middleware/error.js";
import logger from "./middleware/logger.js";
import notFound from "./middleware/notfound.js";
import cors from "cors";
import { syncDatabase } from "./middleware/syncDatabase.js";
import { sync, syncDatabaseRepetedly } from "./DB/sequlize.js";
import { Attendence } from "./models/Attendence.js";
import tr from "date-and-time/locale/tr";
import { User } from "./models/User.js";
import { Auth } from "./middleware/Authentication.js";
export const __filename = fileURLToPath(import.meta.url);
export const __dirname = path.dirname(__filename);
import { connect } from "./DB/Mongoose.js";
import { syncDb } from "./DB/TableSynchroniser.js";
import { updateTraineeStatusJob } from "./Jobs/TraineeStatusUpdateJob.js";
import { backupCronJob } from "./Jobs/mysqldump.js";
const app = express();
const port = process.env.PORT;

syncDatabaseRepetedly();
backupCronJob.start();
updateTraineeStatusJob.start();
//connect();//mongo db connect
syncDb(); //sync database tables

app.use(cors());
//process the json to js objects
app.use(express.json());
//the middleware converts form data into json formats
app.use(express.urlencoded({ extended: false }));

app.use(express.static(path.join(__dirname, "public")));

//Rotes
app.use(logger);

app.use(syncDatabase);

//app.use(Auth);

app.use("/auth", auth);

app.use("/api/trainee", trainees);

app.use("/api/department", depatments);

app.use("/api/programs", programs);

app.use("/api/institutes", institutes);

app.use("/api/periods", periods);

app.use("/api/attendence", attendence);

app.use("/api/calender/", holidays);

app.use("/api/interview", interview);

app.use("/api/payments", payments);

app.use("/api/test", test);

//unknown error handler(route not found)
app.use(notFound);

//error handler
app.use(errorHandler);

app.listen(port, () => {
  console.log("server running on " + port);
});
