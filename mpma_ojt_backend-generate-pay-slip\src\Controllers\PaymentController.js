import { z } from "zod";
import { promisePool } from "../DB/Database.js";
import { getTrainee } from "./TraineeController.js";
import { ErrorWithStatus } from "../ErrorWithStatus.js";
import { getWorkingDays } from "./CalenderController.js";
import { getAttendeceCount } from "./AttendenceController.js";
import XLSX from "xlsx";
import fs from "fs";

const payAmountperDay = 500; // Default payment per day

export const storeTraineeBankDetails = async (req, res, next) => {
  const schema = z.object({
    traineeId: z.coerce.number(),
    name: z.string().min(1).max(100),
    accNo: z.coerce.number(),
    branchCode: z.coerce.number(),
  });
  try {
    const data = schema.parse(req.body);
    const trainee = await getTrainee(data.traineeId);
    if (trainee) {
      const [results] = await promisePool.query(
        "INSERT INTO bank_details(trainee_id,name,branch_code,acc_no) VALUES (?,?,?,?);",
        [trainee.id, data.name, data.branchCode, data.accNo]
      );
      return res.status(201).json(results);
    } else {
      throw new ErrorWithStatus(404, "trainee not found");
    }
  } catch (error) {
    next(error);
  }
};

export const showBankDetails = async (req, res, next) => {
  const schema = z.object({
    traineeId: z.coerce.number(),
  });

  try {
    const data = schema.parse(req.params);
    const trainee = await getTrainee(data.traineeId);
    if (trainee) {
      const bank_details = await getBankDetailsByTraineeId(trainee.id);
      if (bank_details) {
        return res.status(200).json(bank_details);
      } else {
        throw new ErrorWithStatus(
          404,
          "no bank details found with the trainee id"
        );
      }
    } else {
      throw new ErrorWithStatus(404, "no trainee found with the id");
    }
  } catch (error) {
    next(error);
  }
};

/**
 * returns the bank details of the trainee
 * @param {number} id trainee id
 * @returns {object | null} returns bankDetails ,null if not bank detail was found
 */
const getBankDetailsByTraineeId = async (id) => {
  const [bank_details] = await promisePool.query(
    "SELECT * FROM bank_details where trainee_id=?;",
    [id]
  );
  if (bank_details && bank_details.length > 0) {
    return bank_details[0];
  } else {
    return null;
  }
};

export const updateBankDetails = async (req, res, next) => {
  const schema = z.object({
    traineeId: z.coerce.number(),
    name: z.string().min(1).max(100),
    accNo: z.string().regex(/^[0-9]+$/),
    branchCode: z.string().regex(/^[0-9]+$/),
  });
  try {
    const data = schema.parse({ ...req.body, traineeId: req.params.traineeId });
    const trainee = await getTrainee(data.traineeId);
    if (!trainee) {
      throw new ErrorWithStatus(404, "trainee not found");
    }
    const [result] = await promisePool.query(
      "update bank_details set name=?,branch_code=?,acc_no=? where trainee_id=?;",
      [data.name, data.branchCode, data.accNo, trainee.id]
    );
    return res.status(200).json(result);
  } catch (error) {
    next(error);
  }
};

export const generateSlip = async (req, res, next) => {
  const schema = z.object({
    year: z.coerce.number(),
    month: z.coerce.number(),
    date: z.coerce.date(),
    traineeIds: z.array(z.number()),
  });

  try {
    const data = schema.parse(req.body);
    data.year = data.year.toString();

    let dateShort = data.date.toISOString().slice(2, 10).replace(/-/g, ""); //YYMMDD
    //let dateShort = data.date.toISOString().slice(2,4) + data.date.toISOString().slice(8,10) + data.date.toISOString().slice(5,7);  //YYDDMM

    // Process all trainees in parallel using Promise.all
    const traineeDataPromises = data.traineeIds.map(async (trainee_id) => {
      return await getSlipDetails(trainee_id, data);
    });

    const traineeData = await Promise.all(traineeDataPromises);
    const validData = traineeData.filter(Boolean);

    // Separate the data into arrays
    const bankCodes = validData.map((data) => data.bankCode);
    const accNos = validData.map((data) => data.accNo);
    const branchCodes = validData.map((data) => data.branchCode);
    const names = validData.map((data) => data.name);
    const payments = validData.map((data) => data.payment);
    const years = validData.map((data) => data.year);

    const CommonString =
      "SLR7010660000000001917SriLankaPortsAuthori" + " ".repeat(30);

    // Create content row by row
    let content = "";
    for (let i = 0; i < bankCodes.length; i++) {
      content += `0000${bankCodes[i]}${branchCodes[i]
        .toString()
        .padStart(3, "0")}0000${accNos[i].toString().padStart(8, "0")}${names[i]
        .substring(0, 20)
        .padEnd(20, " ")}${years[i]}${payments[i]
        .toString()
        .padStart(19, "0")}00${CommonString}${dateShort}000000\n`;
    }

    // Create a text file with the content
    const fileName = `slips/${data.year}${data.month}.txt`;

    res.setHeader("Content-type", "text/plain");
    res.setHeader("Content-Disposition", `attachment; filename="${fileName}"`);
    res.send(content);

    //const slip = await
  } catch (error) {
    next(error);
  }
};

const getSlipDetails = async (id, data) => {
  let bankCode = 7010; // BOC bank code
  let accNo;
  let branchCode;
  let name;
  let payment;
  let year = data.year.slice(-2);

  const bank_details = await getBankDetailsByTraineeId(id);
  //console.log("t2:", bank_details);
  if (bank_details !== null) {
    accNo = bank_details.acc_no;
    branchCode = bank_details.branch_code;
  } else {
    accNo = "";
    branchCode = "";
  }

  const [trainee_details] = await promisePool.query(
    `
    SELECT COALESCE(NULLIF(bd.name, 'null'), t.name) AS name
    FROM trainee AS t
    LEFT JOIN bank_details AS bd ON bd.trainee_id = t.id
    WHERE t.id = ?
    LIMIT 1;
    `,
    [id]
  );
  if (trainee_details && trainee_details.length > 0) {
    name = trainee_details[0].name.replace(/[.,]/g, " ");
  } else {
    name = "";
  }

  const paymentDetails = await getPaymentbyTraineeId(id, data.month, data.year);

  if (paymentDetails.payment !== null || paymentDetails.payment !== undefined) {
    payment = Number(paymentDetails.payment).toFixed(0);
  } else {
    payment = "";
  }

  let allowEmptyValues = true;

  let haveAllValue = ![
    bankCode,
    accNo,
    branchCode,
    name,
    payment,
    year,
  ].includes("");
  //console.log("t4:", filternull);

  if (haveAllValue || allowEmptyValues) {
    return {
      bankCode,
      accNo,
      branchCode,
      name,
      payment,
      year,
    };
  }
};

export const getPaymentSummary = async (req, res, next) => {
  const schema = z.object({
    year: z.coerce.number(),
    month: z.coerce.number(),
  });

  try {
    const data = schema.parse(req.query);
    const [trainee_details] = await promisePool.query(
      `
      SELECT payment.trainee_id as trainee_id, trainee.ATT_NO as trainee_attNO, trainee.REG_NO as REG_NO, trainee.name as name,
	      journey.end_date as endDate, payment.AttCount as AttCount, payment.payment as payment FROM payment
        left join trainee on payment.trainee_id = trainee.id
        LEFT JOIN journey ON journey.trainee_id = trainee.id
        where month = ? and year= ?
        ORDER BY trainee_attNO ASC;
      `,
      [data.month, data.year]
    );

    const selectTrainees = trainee_details.slice(0, 350);

    const totalAttCount = selectTrainees.reduce(
      (sum, trainee) => sum + (trainee.AttCount || 0),
      0
    );
    const totalPayment = selectTrainees.reduce(
      (sum, trainee) => sum + Number(trainee.payment || 0),
      0
    );
    const meanPaymentPerDay =
      totalPayment / totalAttCount
        ? (totalPayment / totalAttCount).toFixed(2)
        : 0;

    const allGOVTrainees = await getAllGOVtrainnees(data.month, data.year);

    // Create an array to store trainees without bank details
    const traineesWithoutBank = [];
    const traineesWithoutBank350 = [];
    //const traineeIds = [];

    // Check each trainee's bank details
    for (let trainee of allGOVTrainees) {
      const bank_details = await getBankDetailsByTraineeId(trainee.trainee_id);
      //trainee.bank_details = bank_details;

      if (!bank_details) {
        traineesWithoutBank.push(trainee.AttNo);
      }
    }

    for (let trainee of selectTrainees) {
      const bank_details = await getBankDetailsByTraineeId(trainee.trainee_id);

      //traineeIds.push(trainee.trainee_id);

      if (!bank_details) {
        traineesWithoutBank350.push(trainee.trainee_attNO);
      }
    }

    const result = {
      meanPayment: meanPaymentPerDay,
      traineesWithoutBankDetails: traineesWithoutBank,
      traineesWithoutBank350: traineesWithoutBank350,
      selectTrainees: selectTrainees,
      allGOVTrainees: allGOVTrainees,
    };

    return res.status(200).json(result);
  } catch (error) {
    next(error);
  }
};

export const downloadPayamentDetails = async (req, res, next) => {
  const schema = z.object({
    params: z.object({
      year: z.object({
        value: z.string(),
        label: z.string(),
      }),
      month: z.any({
        value: z.string(),
        label: z.string(),
      }),
      trainees: z.array(z.number()),
    }),
  });

  try {
    const validatedData = schema.parse(req.body);
    const year = parseInt(validatedData.params.year.value, 10);
    const month = parseInt(validatedData.params.month.value, 10);
    const trainees = validatedData.params.trainees;

    if (isNaN(year) || isNaN(month)) {
      return res
        .status(400)
        .json({ success: false, message: "Invalid year or month value" });
    }

    if (!trainees || trainees.length === 0) {
      return res
        .status(400)
        .json({ success: false, message: "No trainee IDs provided" });
    }

    const placeholders = trainees.map(() => "?").join(", ");
    const query = `
      SELECT DISTINCT
        trainee.id AS trainee_id,
        trainee.ATT_NO,
        trainee.REG_NO,
        attendences.date AS att_date,
        attendences.status,
        journey.start_date,
        journey.end_date,
        trainee.name
      FROM
        attendences
      JOIN trainee ON attendences.trainee_id = trainee.id
      JOIN journey ON trainee.id = journey.trainee_id
      WHERE
        MONTH(attendences.date) = ?
        AND YEAR(attendences.date) = ?
        AND trainee.id IN (${placeholders});
    `;
    const values = [month, year, ...trainees];
    const [results] = await promisePool.query(query, values);

    const workingDays = await getWorkingDays(month, year);
    console.log("Working Days:", workingDays);

    // Helper: format any JS Date or date-string into "YYYY-MM-DD" in local time
    const formatDate = (d) => {
      const date = new Date(d);
      const compareDate =
        date.getFullYear() +
        "-" +
        String(date.getMonth() + 1).padStart(2, "0") +
        "-" +
        String(date.getDate()).padStart(2, "0");
      return compareDate;
    };

    // Build map of trainee → attendance statuses
    const traineeMap = {};
    results.forEach((row) => {
      const tid = row.trainee_id;
      if (!traineeMap[tid]) {
        traineeMap[tid] = {
          ATT_NO: row.ATT_NO,
          REG_NO: row.REG_NO,
          name: row.name,
          end_date: formatDate(row.end_date),
          attendance: {},
        };
      }

      const dateStr = formatDate(row.att_date);
      if (workingDays.includes(dateStr)) {
        traineeMap[tid].attendance[dateStr] = row.status === 1 ? 1 : 0;
      } else {
        traineeMap[tid].attendance[dateStr] = 0;
      }
    });

    // Prepare Excel headers and rows
    const headers = [
      "S/NO",
      "ATTN ID",
      "REG NO",
      "END DATE",
      "NAME",
      ...workingDays,
    ];

    const excelData = Object.values(traineeMap)
      .sort((a, b) => String(a.ATT_NO).localeCompare(String(b.ATT_NO))) // Convert to string before comparing
      .map((t, i) => {
        const row = {
          "S/NO": i + 1,
          "ATTN ID": t.ATT_NO,
          "REG NO": t.REG_NO,
          "END DATE": formatDate(t.end_date),
          NAME: t.name,
        };

        workingDays.forEach((day) => {
          const wd = new Date(day); // Convert to Date object
          const sd = new Date(t.start_date); // Convert to Date object
          const ed = new Date(t.end_date); // Convert to Date object

          wd.setHours(0, 0, 0, 0);
          sd.setHours(0, 0, 0, 0);
          ed.setHours(0, 0, 0, 0);

          if (wd <= ed) {
            const mark = t.attendance[formatDate(day)] === 1 ? 1 : 0;
            row[day] = mark;
          } else {
            row[day] = 0;
          }
        });
        return row;
      });

    // Generate and send Excel file
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.json_to_sheet(excelData, { header: headers });

    ws["!cols"] = [
      { width: 5 },
      { width: 10 },
      { width: 10 },
      { width: 12 },
      { width: 30 },
      ...workingDays.map(() => ({ width: 6 })),
      { width: 12 },
      { width: 16 },
    ];

    XLSX.utils.book_append_sheet(wb, ws, `Attendance_${year}_${month}`);
    const buffer = await XLSX.write(wb, { type: "buffer", bookType: "xlsx" });

    try {
      const tempFilePath = await processExcelVBA(buffer, year, month);

      // Read the processed file
      const processedFilePath = tempFilePath; // or where VBA saved the final file
      const processedBuffer = fs.readFileSync(processedFilePath);

      res
        .setHeader(
          "Content-Type",
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        )
        .setHeader(
          "Content-Disposition",
          `attachment; filename=Attendance_${year}_${month}.xlsx`
        )
        .send(processedBuffer);

      // Clean up temporary file
      setTimeout(() => {
        fs.unlink(processedFilePath, (err) => {
          if (err) console.error("Error deleting temp file:", err);
        });
      }, 1000 * 180); // Wait 3 minutes before deleting
    } catch (error) {
      // Log VBA error but don't fail the request
      console.warn(
        "VBA processing failed, sending unprocessed file:",
        error.message
      );

      // Send original buffer as fallback
      res
        .setHeader(
          "Content-Type",
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        )
        .setHeader(
          "Content-Disposition",
          `attachment; filename=Attendance_${year}_${month}_no_formatting.xlsx`
        )
        .send(buffer);
    }
  } catch (error) {
    console.error("Error in generatePayamentDetails:", error);
    next(error);
  }
};

async function processExcelVBA(buffer, year, month) {
  let excel = null;
  let workbook = null;

  const ensureDirectoryExists = (dirPath) => {
    try {
      if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
        console.log(`Created directory: ${dirPath}`);
      }
    } catch (error) {
      console.error(`Error creating directory: ${error}`);
      throw error;
    }
  };

  try {
    // Save Excel file temporarily
    const tempFilePath = `./temp/temp_Attendance_${year}_${month}.xlsx`;

    // First ensure directory exists
    await new Promise((resolve, reject) => {
      ensureDirectoryExists(tempFilePath.replace(/\/[^/]+$/, ""));
      resolve();
    });

    fs.writeFileSync(tempFilePath, buffer);

    const vbaCode = fs.readFileSync("src/Module2.bas", "utf8");

    // Launch Excel
    excel = new winax.Object("Excel.Application");

    excel.EnableEvents = true; // Enable Excel events
    excel.AutomationSecurity = 1;
    excel.Visible = false;
    excel.DisplayAlerts = false;

    workbook = excel.Workbooks.Open(fs.realpathSync(tempFilePath));

    // Add module and insert code
    const vbProj = workbook?.VBProject;

    if (!vbProj) {
      throw new Error("VBProject is undefined. Check Excel macro settings.");
    }

    const vbComp = vbProj.VBComponents.Add(1); // 1 = Module
    vbComp.CodeModule.AddFromString(vbaCode);

    excel.Run("Macro1");
    workbook.Save();

    return tempFilePath;
  } catch (error) {
    console.error("Error processing Excel VBA:", error);
    throw new ErrorWithStatus(500, "Failed to process Excel with VBA");
  } finally {
    workbook.Close(false);
    excel.Quit();
  }
}

export const updatePaymentTable = async (data) => {
  try {
    // First delete existing records for the given year and month
    await promisePool.query(
      `DELETE FROM payment 
       WHERE year = ? AND month = ?`,
      [data.year, data.month]
    );

    const [trainee_details] = await promisePool.query(
      `SELECT trainee.id AS trainee_id
      FROM trainee
      INNER JOIN journey ON trainee.id = journey.trainee_id
      INNER JOIN attendences ON trainee.id = attendences.trainee_id
      WHERE trainee.ATT_NO LIKE '9%'
        AND YEAR(attendences.date) = ?
        AND MONTH(attendences.date) = ?
        AND attendences.date BETWEEN journey.start_date AND journey.end_date
        AND attendences.status = 1
      GROUP BY trainee.id 
      HAVING COUNT(attendences.id) > 1
      ORDER BY trainee.ATT_NO ASC
      LIMIT 350;`,
      [data.year, data.month]
    );

    const traineeIds350 = trainee_details.map((trainee) => trainee.trainee_id);

    const attCount = await getAttendeceCount(
      data.year,
      data.month,
      traineeIds350
    );

    const paymentsArray = attCount.map((att) => ({
      trainee_id: att.trainee_id,
      year: data.year,
      month: data.month,
      AttCount: att.attendance_count,
      payment: att.attendance_count * payAmountperDay,
    }));

    const placeholders = paymentsArray.map(() => "(?, ?, ?, ?, ?)").join(", ");

    // Flatten the values into a single array
    const values = paymentsArray.flatMap((p) => [
      p.trainee_id,
      p.year,
      p.month,
      p.AttCount,
      p.payment,
    ]);

    const [result] = await promisePool.query(
      `
      INSERT INTO payment (trainee_id, year, month, AttCount, payment)
      VALUES ${placeholders}
      `,
      values
    );
  } catch (error) {
    console.error("Error updating payment table:", error);
    throw new ErrorWithStatus(500, "Failed to update payment table");
  }
};

export const removeFromPaymentList = async (req, res, next) => {
  const schema = z.object({
    id: z.coerce.number(),
    month: z.coerce.number(),
    year: z.coerce.number(),
  });

  const data = schema.parse(req.body);

  try {
    // Delete current trainee from payment
    await promisePool.query(
      `DELETE FROM payment WHERE trainee_id = ? AND month = ? AND year = ?`,
      [data.id, data.month, data.year]
    );

    return res
      .status(200)
      .json({ success: true, message: `${data.id} Trainee removed.` });
  } catch (error) {
    console.error("removeFromPaymentList error:", error);
    return res.status(500).json({ success: false, message: error.message });
  }
};

export const addToPaymentList = async (req, res, next) => {
  const schema = z.object({
    id: z.coerce.number(),
    month: z.coerce.number(),
    year: z.coerce.number(),
  });

  const data = schema.parse(req.body);

  try {
    const totalAtt = await getAttendeceCount(data.year, data.month, [data.id]);
    const paymentDetails = await getPaymentbyTraineeId(
      undefined,
      data.month,
      data.year
    );

    const attCount = totalAtt[0].attendance_count;
    const payment =
      attCount * (paymentDetails[0].payment / paymentDetails[0].AttCount);

    const [recordCount] = await promisePool.query(
      `
      select count(*) from payment where year=? and month=?;
      `,
      [data.year, data.month]
    );

    if (recordCount[0]["count(*)"] >= 350) {
      return res.status(400).json({
        success: false,
        message: "Payment list is full for this month.",
      });
    } else {
      // Insert new payment record
      await promisePool.query(
        `INSERT INTO payment (trainee_id, year, month, AttCount, payment)
       VALUES (?, ?, ?, ?, ?)`,
        [data.id, data.year, data.month, attCount, payment]
      );

      return res
        .status(200)
        .json({ success: true, message: `${data.id} Trainee added.` });
    }
  } catch (error) {
    console.error("addToPaymentList error:", error);
    return res.status(500).json({ success: false, message: error.message });
  }
};

const getAllGOVtrainnees = async (month, year) => {
  try {
    const [GOVTrainees] = await promisePool.query(
      `
      SELECT trainee.id AS trainee_id, trainee.ATT_NO as AttNo, trainee.REG_NO as REG_NO, trainee.name as name,
      MAX(journey.end_date) as endDate
      FROM trainee
      INNER JOIN journey ON trainee.id = journey.trainee_id
      INNER JOIN attendences ON trainee.id = attendences.trainee_id
      WHERE trainee.ATT_NO LIKE '9%'
        AND YEAR(attendences.date) = ?
        AND MONTH(attendences.date) = ?
        AND attendences.date BETWEEN journey.start_date AND journey.end_date
        AND attendences.status = 1
      GROUP BY trainee.id 
      ORDER BY trainee.ATT_NO ASC;
      `,
      [year, month]
    );

    // Get attendance counts for all trainees
    const attendanceCounts = await getAttendeceCount(
      year,
      month,
      GOVTrainees.map((t) => t.trainee_id)
    );

    // Merge the attendance counts with trainee data
    const result = GOVTrainees.map((trainee) => {
      const attendance = attendanceCounts.find(
        (a) => a.trainee_id === trainee.trainee_id
      );
      return {
        trainee_id: trainee.trainee_id,
        AttNo: trainee.AttNo,
        REG_NO: trainee.REG_NO,
        name: trainee.name,
        endDate: trainee.endDate,
        AttCount: attendance ? attendance.attendance_count : 0,
      };
    }).filter((trainee) => trainee.AttCount > 1);

    return result;
  } catch (error) {
    console.error("Error in getAllGOVtrainnees:", error);
    throw error;
  }
};

const getPaymentbyTraineeId = async (ids, month, year) => {
  let query;
  let params;

  if (!ids || (Array.isArray(ids) && ids.length === 0)) {
    // No IDs provided: return all for month/year
    query = `SELECT * FROM payment WHERE month = ? AND year = ?;`;
    params = [month, year];
  } else {
    const idArray = Array.isArray(ids) ? ids : [ids];
    if (idArray.length === 1) {
      query = `SELECT * FROM payment WHERE trainee_id = ? AND month = ? AND year = ?;`;
      params = [idArray[0], month, year];
    } else {
      const placeholders = idArray.map(() => "?").join(", ");
      query = `SELECT * FROM payment WHERE trainee_id IN (${placeholders}) AND month = ? AND year = ?;`;
      params = [...idArray, month, year];
    }
  }

  const [payment] = await promisePool.query(query, params);

  // Return type based on input
  if (!ids || (Array.isArray(ids) && ids.length === 0)) {
    return payment; // all records for month/year
  } else if (Array.isArray(ids) && ids.length > 1) {
    return payment; // array of records
  } else {
    return payment && payment.length > 0 ? payment[0] : null;
  }
};

export const updatePaymentPerDay = async (req, res, next) => {
  const schema = z.object({
    month: z.coerce.number(),
    year: z.coerce.number(),
    newAmount: z.coerce.number().positive(),
  });

  const data = schema.parse(req.body);

  try {
    if (typeof data.newAmount !== "number" || data.newAmount < 0) {
      throw new ErrorWithStatus(400, "Invalid payment amount");
    }

    // Get all IDs to update
    const [rows] = await promisePool.query(
      "SELECT id FROM payment WHERE year = ? AND month = ?;",
      [data.year, data.month]
    );
    const ids = rows.map((r) => r.id);

    if (ids.length > 0) {
      const placeholders = ids.map(() => "?").join(", ");
      await promisePool.query(
        `UPDATE payment SET payment = AttCount * ? WHERE id IN (${placeholders});`,
        [data.newAmount, ...ids]
      );
    }

    return res
      .status(200)
      .json({ success: true, message: "Payment per day updated successfully" });
  } catch (error) {
    console.error("Error updating payment per day:", error);
    return res.status(500).json({ success: false, message: error.message });
  }
};

/*
export const getDateSummary = async (req, res, next) => {
  try {
    const [years] = await promisePool.query(
      "SELECT distinct payment.year as year FROM payment ORDER BY year ASC;"
    );
    
    let summary = [];
    for (const year of years) {
      const [months] = await promisePool.query(
        "SELECT distinct payment.month as month FROM payment WHERE year = ? ORDER BY month ASC;",
        [year.year]
      );
      
      summary.push({
        year: year.year,
        months: months.map(m => m.month)
      });
    }
    
    return res.status(200).json(summary);
  } catch (error) {
    next(error);
  }
};*/
