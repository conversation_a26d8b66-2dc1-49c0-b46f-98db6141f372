import { string, z } from "zod";
import { promisePool } from "../DB/Database.js";

const schema = z.object({
  name: string().min(2),
  code: z.union([
    z
      .string()
      .regex(/^[a-zA-Z]+$/, {
        message: "cannot contain any spaces or numbers or special charachters",
      })
      .toUpperCase()
      .min(2)
      .max(10),
    z.string().length(0),
  ]),
  type: z.enum(["naita", "cinec", "normal" , "smti"]),
});

export const store = async (req, res, next) => {
  try {
    console.log(req.body);
    const data = schema.parse(req.body);
    let query;
    if (data.type == "naita") {
      query = "INSERT INTO `training_program`(`name`, `special_code`) VALUES (?, ?)";
    } else {
      query = "INSERT INTO `training_program`(`name`, `code`) VALUES (?, ?)";
    }
    const [result] = await promisePool.query(query, [data.name, data.code]);
    return res.status(201).json(result);
  } catch (e) {
    next(e);
  }
};

export const index = async (req, res, next) => {
  const [programs] = await promisePool.query("SELECT * FROM training_program");
  return res.status(200).json(programs);
};

export const remove = async (req, res, next) => {
  const id = req.params.id;
  const result = await promisePool.query("DELETE FROM training_program where id=?", [id]);
  return res.status(200).json(result);
};
