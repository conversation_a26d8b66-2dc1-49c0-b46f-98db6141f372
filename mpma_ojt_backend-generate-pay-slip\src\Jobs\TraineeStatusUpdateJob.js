import { <PERSON>ronJob } from "cron";
import { promisePool } from "../DB/Database.js";

const updateTraineeStatus = async () => {
  try {
    const result = await promisePool.query(
      `
      UPDATE trainee
      JOIN journey ON trainee.id = journey.trainee_id
      SET trainee.status = CASE
          WHEN journey.end_date < CURDATE() THEN 0
          WHEN journey.start_date <= CURDATE() AND CURDATE() <= journey.end_date THEN 1
          ELSE trainee.status
      END;
      `
    );
    console.log("update job status", result);
  } catch (error) {
    console.log("failed updating status");
    console.log(error);
  }
};

export const updateTraineeStatusJob = new CronJob(
  "0 0 */2 * * *", // Run every day at 12:00  ( 0 th minute every day)
  async () => {
    console.log(
      "backup Job started executed at:",
      new Intl.DateTimeFormat("en-US", {
        timeZone: "Asia/Colombo",
        dateStyle: "full",
        timeStyle: "long",
      }).format(new Date())
    );
    try {
      await updateTraineeStatus();
    } catch (error) {
      console.log(error);
      console.log(
        "backup Job failed  at:",
        new Intl.DateTimeFormat("en-US", {
          timeZone: "Asia/Colombo",
          dateStyle: "full",
          timeStyle: "long",
        }).format(new Date())
      );
    }
  },
  false,
  true, // Start the job immediately
  "Asia/Colombo"
);
