.trainee-grid {
  display: grid;
  gap: 0.5rem;
  /* Single column by default for mobile */
  grid-template-columns: 1fr;
}

/* 2 columns for medium screens */
@media (min-width: 768px) {
  .trainee-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* 3 columns for large screens */
@media (min-width: 992px) {
  .trainee-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

.min-width-modal .modal-dialog {
  width: fit-content !important;
  min-width: unset !important;
  max-width: 98vw !important;
  margin: auto;
}

.min-width-modal .modal-content,
.min-width-modal .modal-body {
  width: fit-content !important;
  min-width: unset !important;
  max-width: 98vw !important;
  padding: 0.5rem 1rem;
}

.small-accordion-header .accordion-button {
  padding-top: 0.2rem;
  padding-bottom: 0.2rem;
  min-height: 1.5rem;
  font-size: 1.1rem; /* optional, for consistency */
}

.sticky-bottom-accordion {
  position: sticky;
  bottom: 0;
  z-index: 10;
  background: #fff;
}

.custom-accordion-header .accordion-button {
  background-color: #9ecdfc !important;
  color: rgb(54, 52, 52) !important;
  font-weight: bold;
}