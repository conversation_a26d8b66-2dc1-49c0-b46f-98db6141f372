import { jwtVerify } from "jose";
const secret = new TextEncoder().encode(process.env.AUTH_SECRET);

export const Auth = async (req, res, next) => {
  try {
    const authorizationHeader = req.get("Authorization");
    console.log(authorizationHeader);
    await verifyToken(authorizationHeader);
    next();
  } catch (error) {
    next(error);
  }
};

const verifyToken = async (token) => {
  try {
    const { payload, protectedHeader } = await jwtVerify(token, secret);
    console.log("payload-", payload);
  } catch (error) {
    console.log("failed to verify");
    throw error;
  }
};
