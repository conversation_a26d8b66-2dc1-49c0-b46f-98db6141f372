const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('trainee', {
    id: {
      autoIncrement: true,
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true
    },
    name: {
      type: DataTypes.STRING(500),
      allowNull: false
    },
    ATT_NO: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    REG_NO: {
      type: DataTypes.STRING(25),
      allowNull: false,
      unique: "REG_NO"
    },
    NIC_NO: {
      type: DataTypes.STRING(20),
      allowNull: false
    },
    contact_no: {
      type: DataTypes.STRING(10),
      allowNull: false
    },
    status: {
      type: DataTypes.BOOLEAN,
      allowNull: false
    },
    institute_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'institute',
        key: 'id'
      }
    },
    training_program_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'training_program',
        key: 'id'
      }
    }
  }, {
    sequelize,
    tableName: 'trainee',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "id" },
        ]
      },
      {
        name: "ATT_NO",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "ATT_NO" },
          { name: "NIC_NO" },
        ]
      },
      {
        name: "REG_NO",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "REG_NO" },
        ]
      },
      {
        name: "institute_id",
        using: "BTREE",
        fields: [
          { name: "institute_id" },
        ]
      },
      {
        name: "training_program_id",
        using: "BTREE",
        fields: [
          { name: "training_program_id" },
        ]
      },
    ]
  });
};
