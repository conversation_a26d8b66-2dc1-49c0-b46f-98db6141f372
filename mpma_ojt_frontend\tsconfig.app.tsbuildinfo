{"root": ["./src/api.ts", "./src/helpers.ts", "./src/main.tsx", "./src/types.ts", "./src/vite-env.d.ts", "./src/components/bankdetailsform.tsx", "./src/components/errorhandler.tsx", "./src/components/landingpage.tsx", "./src/components/callenders/holidays/markholidays.tsx", "./src/components/callenders/models/addholiday.tsx", "./src/components/modals/adddepartmentmodal.tsx", "./src/components/modals/newinterviewmodal.tsx", "./src/components/navbars/mainnav.tsx", "./src/components/tables/flippablecell/flipabletablecell.tsx", "./src/components/user/userform/userform.tsx", "./src/components/user/userform/userupdateform.tsx", "./src/components/user/userprofile/userprofile.tsx", "./src/components/traineeform/addinstitutemodal.tsx", "./src/components/traineeform/addperiodmodal.tsx", "./src/components/traineeform/addprogrammodal.tsx", "./src/components/traineeform/nic.tsx", "./src/components/traineeform/regnumbers.tsx", "./src/components/traineeform/trainee.ts", "./src/components/traineeform/traineeformv2.tsx", "./src/components/ui/loader/loader.tsx", "./src/components/ui/loader/miniloader.tsx", "./src/pages/attendencespage.tsx", "./src/pages/base.tsx", "./src/pages/calenderpage.tsx", "./src/pages/departmentpage.tsx", "./src/pages/departmentspage.tsx", "./src/pages/generatepaymentslip.tsx", "./src/pages/loginpage.tsx", "./src/pages/notificationpage.tsx", "./src/pages/paymentspage.tsx", "./src/pages/profilepage.tsx", "./src/pages/traineeaddbankdetailspage.tsx", "./src/pages/traineeaddschedulepage.tsx", "./src/pages/traineebankdetailsupdatepage.tsx", "./src/pages/traineedetailsaddpage.tsx", "./src/pages/traineedetailsaddpagev2.tsx", "./src/pages/traineedetailspage.tsx", "./src/pages/updatetraineedetailpage.tsx", "./src/pages/uploadattendencesheet.tsx", "./src/pages/userprofilepage.tsx", "./src/pages/viewpaymentdetails.tsx", "./src/pages/viewtraineespage.tsx", "./src/pages/admin/adduserpage.tsx", "./src/pages/admin/traineedetailsupdate.tsx", "./src/pages/admin/traineedetailsupdatepage.tsx", "./src/pages/admin/userupdatepage.tsx", "./src/pages/admin/viewuserspage.tsx", "./src/features/authentication/components/login/logincomponent.tsx", "./src/features/authentication/hooks/useauthentication.ts", "./src/features/interview/interviewtables.tsx", "./src/features/interview/editinterviewpage.tsx", "./src/features/interview/interviewform.tsx", "./src/features/interview/interviewnic.tsx", "./src/features/interview/interviewpage.tsx", "./src/features/interview/viewinterviewpage.tsx", "./src/features/updatetrainee/admin/updatetrainees.tsx", "./src/features/updatetrainee/user/updatetrainees.tsx", "./src/layout/containers/main_container/maincontainer.tsx", "./src/layout/containers/sub_container/subcontainer.tsx", "./src/layout/header/header.tsx", "./src/layout/sidebar/sidebar.tsx", "./src/loaders/attendenceloader.ts", "./src/loaders/departmentloader.ts", "./src/loaders/holidaysloader.ts", "./src/loaders/interviewloader.ts", "./src/loaders/paymentsliploader.ts", "./src/loaders/profilepageloader.ts", "./src/loaders/traineesloader.ts", "./src/loaders/updatepageloader.ts", "./src/loaders/userloaders.ts", "./src/loaders/inboxloader.ts"], "version": "5.6.2"}