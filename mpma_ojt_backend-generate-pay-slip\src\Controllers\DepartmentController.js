import { z, Zod<PERSON>rror } from "zod";
import { promisePool } from "../DB/Database.js";
import { Department, Interview, Schedule, Trainee } from "../models/index.js";
import { Op } from "sequelize";
import tr from "date-and-time/locale/tr";
import { getMondays } from "./CalenderController.js";

const schema = z.object({
  name: z.string().min(2),
  max_count: z.coerce.number().gt(0),
});

export const index = async (req, res, next) => {
  try {
    const results = await promisePool.query("SELECT * FROM `department`");
    return res.status(200).json(results[0]);
  } catch (error) {
    next(error);
  }
};

export const store = async (req, res, next) => {
  try {
    const data = schema.parse(req.body);
    const [results] = await promisePool.query(
      "insert into department(name,max_count) values (?,?)",
      [data.name, data.max_count]
    );
    return res.status(201).json(results);
  } catch (e) {
    next(e);
  }
};

export const getDepartmentSummary = async (req, res, next) => {
  try {
    //need to get the active count of trainees in the department.
    const [results] = await promisePool.query(`
      select 
        department.name,
        department.id as dep_id,
        department.max_count,
	      (
          select count(trainee.id) from
          department,trainee,schedule
            where
          schedule.trainee_id=trainee.id and
          schedule.start_date<=curdate() and
	        schedule.end_date>=curdate() and
          schedule.department_id=department.id and
          schedule.department_id=dep_id and trainee.status=1 
        ) as active_count,
        (
          select count(interviews.NIC) from 
          interviews 
            where
          interviews.departmentId=dep_id
        ) as interview_count
      from department;
      `);
    return res.status(200).json(results);
  } catch (error) {
    next(error);
  }
};

export const getInterviews = async (req, res, next) => {
  const schema = z.object({
    id: z.coerce.number(),
  });
  console.log(" get interviews ", req.params);
  try {
    const data = schema.parse(req.params);
    const interviews = await Interview.findAll({
      where: {
        departmentId: data.id,
      },
      order: [["date", "ASC"]],
    });
    console.log(interviews);
    return res.status(200).json(interviews);
  } catch (error) {
    next(error);
  }
};

export const show = async (req, res, next) => {
  const schema = z.object({
    id: z.coerce.number(),
  });
  try {
    const data = schema.parse(req.params);
    const department = await Department.findByPk(data.id, {
      include: [
        {
          model: Interview,
          separate: true,
          order: [["date", "ASC"]],
        },
        {
          model: Schedule,
          where: {
            start_date: {
              [Op.lte]: new Date(),
            },
            end_date: {
              [Op.gte]: new Date(), //computed property usage as identifier
            },
          },
          separate: true,
          order: [["end_date", "ASC"]],
          include: [
            {
              model: Trainee,
              where: {
                status: 1,
              },
            },
          ],
        },
      ],
      logging: console.log,
    });
    return res.status(200).json(department);
  } catch (error) {
    next(error);
  }
};

export const getinterviewDateSummary = async (req, res, next) => {
  const schema = z.object({
    id: z.coerce.number(),
  });
  try {
    const data = schema.parse(req.params);
    const mondays = await getMondays();
    const summaryPromises = mondays.map(async (monday) => {
      const query = `
        select 
        (
          select 
            count(trainee.id)
            from 
              schedule,trainee
            where 
              schedule.trainee_id=trainee.id and
              trainee.status=1 and schedule.end_date>"${monday}" and
              schedule.department_id=${data.id}
        ) as departmentCount,
         (select count(NIC) from interviews where date="${monday}") as interviewCountAtMonday,
        (
          select 
            count(NIC) 
            from
              interviews
            where
              interviews.departmentId=${data.id} and
              date<="${monday}"
        ) as interviewedCount,
        department.max_count
          from
            department
          where
            id=${data.id};
        `;
      console.log(query);
      const [result] = await promisePool.query(query);
      const s = {
        date: monday,
        max_count: result[0].max_count,
        departmentCount: result[0].departmentCount,
        interviews: result[0].interviewedCount,
        headCount: result[0].interviewCountAtMonday,
      };
      console.log(s);
      return s;
    });
    const summary = await Promise.all(summaryPromises);
    //console.log(summary);
    return res.status(200).json(summary);
  } catch (error) {
    next(error);
  }
};
