import { Router } from "express";
import { deleteRecord, show, showTest, store, update, deleteByID, updateByID } from "../Controllers/InterviewController.js";
import { Auth } from "../Core/AuthWrapper.js";
import { INTERVIEW_MODIFY, INTERVIEW_VIEW } from "../Core/Permisions.js";

const router = Router();

router.post("/", await <PERSON>th([INTERVIEW_MODIFY]), store);

router.delete("/:nic", await <PERSON>th([INTERVIEW_MODIFY]), deleteRecord);

router.delete("/by-id/:id", await <PERSON>th([INTERVIEW_MODIFY]), deleteByID);

router.put("/:nic", await <PERSON>th([INTERVIEW_MODIFY]), update);

router.put("/by-id/:id", await <PERSON>th([INTERVIEW_MODIFY]), updateByID);

router.get("/",  show);

export default router;
