import React, { useEffect, useState } from 'react';
import { Modal, Button, ProgressBar } from 'react-bootstrap';
import { useSession } from '../contexts/SessionContext';

const SessionTimeoutWarning: React.FC = () => {
  const {
    isSessionWarningVisible,
    sessionTimeLeft,
    hideSessionWarning,
    logout
  } = useSession();

  const [countdown, setCountdown] = useState<number>(0);

  // Convert milliseconds to minutes and seconds
  const formatTime = (milliseconds: number) => {
    const totalSeconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  // Calculate progress percentage for progress bar
  const getProgressPercentage = () => {
    const fiveMinutesInMs = 5 * 60 * 1000; // 5 minutes in milliseconds
    const percentage = (sessionTimeLeft / fiveMinutesInMs) * 100;
    return Math.max(0, Math.min(100, percentage));
  };

  // Update countdown every second when warning is visible
  useEffect(() => {
    if (isSessionWarningVisible) {
      setCountdown(sessionTimeLeft);
      
      const interval = setInterval(() => {
        setCountdown(prev => {
          const newValue = prev - 1000;
          return newValue > 0 ? newValue : 0;
        });
      }, 1000);

      return () => clearInterval(interval);
    }
  }, [isSessionWarningVisible, sessionTimeLeft]);

  const handleLogout = () => {
    logout();
    window.location.href = '/login';
  };

  const handleDismiss = () => {
    hideSessionWarning();
  };

  if (!isSessionWarningVisible) {
    return null;
  }

  const progressVariant = countdown > 2 * 60 * 1000 ? 'warning' : 'danger'; // Change color when less than 2 minutes

  return (
    <Modal 
      show={isSessionWarningVisible} 
      backdrop="static" 
      keyboard={false}
      centered
      size="md"
    >
      <Modal.Header className="bg-warning text-dark">
        <Modal.Title>
          <i className="bi bi-exclamation-triangle-fill me-2"></i>
          Session Timeout Warning
        </Modal.Title>
      </Modal.Header>
      
      <Modal.Body className="text-center">
        <div className="mb-4">
          <i className="bi bi-clock-history" style={{ fontSize: '3rem', color: '#ffc107' }}></i>
        </div>
        
        <h5 className="mb-3">Your session is about to expire!</h5>
        
        <p className="mb-3">
          Your session will automatically expire in:
        </p>
        
        <div className="mb-3">
          <h3 className={`mb-2 ${countdown <= 60000 ? 'text-danger' : 'text-warning'}`}>
            {formatTime(countdown)}
          </h3>
          <ProgressBar 
            now={getProgressPercentage()} 
            variant={progressVariant}
            style={{ height: '10px' }}
            className="mb-2"
          />
          <small className="text-muted">
            Time remaining until automatic logout
          </small>
        </div>
        
        <p className="text-muted">
          You will be automatically logged out when the timer reaches zero.
        </p>
      </Modal.Body>
      
      <Modal.Footer className="justify-content-center">
        <Button
          variant="outline-secondary"
          onClick={handleDismiss}
          className="me-2"
        >
          <i className="bi bi-x-circle me-2"></i>
          Dismiss Warning
        </Button>

        <Button
          variant="danger"
          onClick={handleLogout}
        >
          <i className="bi bi-box-arrow-right me-2"></i>
          Logout Now
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default SessionTimeoutWarning;
