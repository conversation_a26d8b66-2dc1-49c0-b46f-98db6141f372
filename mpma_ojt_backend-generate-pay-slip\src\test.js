const MAX_PRIME = 1000000;

const random = (max) => Math.floor(Math.random() * max);
console.log("start");

let primes = generatePrimes(1000000);
console.log(primes);

console.log("end");

function isPrime(n) {
  for (let i = 2; i <= Math.sqrt(n); i++) {
    if (n % i === 0) {
      return false;
    }
  }
  return n > 1;
}

function generatePrimes(quota) {
  const primes = [];
  while (primes.length < quota) {
    const candidate = random(MAX_PRIME);
    if (isPrime(candidate)) {
      primes.push(candidate);
    }
  }
  return primes;
}
