{"version": 3, "sources": ["../../@fullcalendar/daygrid/internal.js", "../../@fullcalendar/daygrid/index.js"], "sourcesContent": ["import { DateComponent, getStickyHeaderDates, ViewContainer, SimpleScrollGrid, getStickyFooterScrollbar, renderScrollShim, createFormatter, BaseComponent, StandardEvent, buildSegTimeText, EventContainer, getSegAnchorAttrs, memoize, MoreLinkContainer, getSegMeta, getUniqueDomId, setRef, DayCellContainer, WeekNumberContainer, buildNavLinkAttrs, hasCustomDayCellContent, addMs, intersectRanges, addDays, SegHierarchy, buildEntryKey, intersectSpans, RefMap, sortEventSegs, isPropsEqual, buildEventRangeKey, BgEvent, renderFill, PositionCache, NowTimer, formatIsoMonthStr, formatDayString, Slicer, DayHeader, DaySeriesModel, DayTableModel, DateProfileGenerator, addWeeks, diffWeeks, injectStyles } from '@fullcalendar/core/internal.js';\nimport { createRef, createElement, Fragment } from '@fullcalendar/core/preact.js';\n\n/* An abstract class for the daygrid views, as well as month view. Renders one or more rows of day cells.\n----------------------------------------------------------------------------------------------------------------------*/\n// It is a manager for a Table subcomponent, which does most of the heavy lifting.\n// It is responsible for managing width/height.\nclass TableView extends DateComponent {\n    constructor() {\n        super(...arguments);\n        this.headerElRef = createRef();\n    }\n    renderSimpleLayout(headerRowContent, bodyContent) {\n        let { props, context } = this;\n        let sections = [];\n        let stickyHeaderDates = getStickyHeaderDates(context.options);\n        if (headerRowContent) {\n            sections.push({\n                type: 'header',\n                key: 'header',\n                isSticky: stickyHeaderDates,\n                chunk: {\n                    elRef: this.headerElRef,\n                    tableClassName: 'fc-col-header',\n                    rowContent: headerRowContent,\n                },\n            });\n        }\n        sections.push({\n            type: 'body',\n            key: 'body',\n            liquid: true,\n            chunk: { content: bodyContent },\n        });\n        return (createElement(ViewContainer, { elClasses: ['fc-daygrid'], viewSpec: context.viewSpec },\n            createElement(SimpleScrollGrid, { liquid: !props.isHeightAuto && !props.forPrint, collapsibleWidth: props.forPrint, cols: [] /* TODO: make optional? */, sections: sections })));\n    }\n    renderHScrollLayout(headerRowContent, bodyContent, colCnt, dayMinWidth) {\n        let ScrollGrid = this.context.pluginHooks.scrollGridImpl;\n        if (!ScrollGrid) {\n            throw new Error('No ScrollGrid implementation');\n        }\n        let { props, context } = this;\n        let stickyHeaderDates = !props.forPrint && getStickyHeaderDates(context.options);\n        let stickyFooterScrollbar = !props.forPrint && getStickyFooterScrollbar(context.options);\n        let sections = [];\n        if (headerRowContent) {\n            sections.push({\n                type: 'header',\n                key: 'header',\n                isSticky: stickyHeaderDates,\n                chunks: [{\n                        key: 'main',\n                        elRef: this.headerElRef,\n                        tableClassName: 'fc-col-header',\n                        rowContent: headerRowContent,\n                    }],\n            });\n        }\n        sections.push({\n            type: 'body',\n            key: 'body',\n            liquid: true,\n            chunks: [{\n                    key: 'main',\n                    content: bodyContent,\n                }],\n        });\n        if (stickyFooterScrollbar) {\n            sections.push({\n                type: 'footer',\n                key: 'footer',\n                isSticky: true,\n                chunks: [{\n                        key: 'main',\n                        content: renderScrollShim,\n                    }],\n            });\n        }\n        return (createElement(ViewContainer, { elClasses: ['fc-daygrid'], viewSpec: context.viewSpec },\n            createElement(ScrollGrid, { liquid: !props.isHeightAuto && !props.forPrint, forPrint: props.forPrint, collapsibleWidth: props.forPrint, colGroups: [{ cols: [{ span: colCnt, minWidth: dayMinWidth }] }], sections: sections })));\n    }\n}\n\nfunction splitSegsByRow(segs, rowCnt) {\n    let byRow = [];\n    for (let i = 0; i < rowCnt; i += 1) {\n        byRow[i] = [];\n    }\n    for (let seg of segs) {\n        byRow[seg.row].push(seg);\n    }\n    return byRow;\n}\nfunction splitSegsByFirstCol(segs, colCnt) {\n    let byCol = [];\n    for (let i = 0; i < colCnt; i += 1) {\n        byCol[i] = [];\n    }\n    for (let seg of segs) {\n        byCol[seg.firstCol].push(seg);\n    }\n    return byCol;\n}\nfunction splitInteractionByRow(ui, rowCnt) {\n    let byRow = [];\n    if (!ui) {\n        for (let i = 0; i < rowCnt; i += 1) {\n            byRow[i] = null;\n        }\n    }\n    else {\n        for (let i = 0; i < rowCnt; i += 1) {\n            byRow[i] = {\n                affectedInstances: ui.affectedInstances,\n                isEvent: ui.isEvent,\n                segs: [],\n            };\n        }\n        for (let seg of ui.segs) {\n            byRow[seg.row].segs.push(seg);\n        }\n    }\n    return byRow;\n}\n\nconst DEFAULT_TABLE_EVENT_TIME_FORMAT = createFormatter({\n    hour: 'numeric',\n    minute: '2-digit',\n    omitZeroMinute: true,\n    meridiem: 'narrow',\n});\nfunction hasListItemDisplay(seg) {\n    let { display } = seg.eventRange.ui;\n    return display === 'list-item' || (display === 'auto' &&\n        !seg.eventRange.def.allDay &&\n        seg.firstCol === seg.lastCol && // can't be multi-day\n        seg.isStart && // \"\n        seg.isEnd // \"\n    );\n}\n\nclass TableBlockEvent extends BaseComponent {\n    render() {\n        let { props } = this;\n        return (createElement(StandardEvent, Object.assign({}, props, { elClasses: ['fc-daygrid-event', 'fc-daygrid-block-event', 'fc-h-event'], defaultTimeFormat: DEFAULT_TABLE_EVENT_TIME_FORMAT, defaultDisplayEventEnd: props.defaultDisplayEventEnd, disableResizing: !props.seg.eventRange.def.allDay })));\n    }\n}\n\nclass TableListItemEvent extends BaseComponent {\n    render() {\n        let { props, context } = this;\n        let { options } = context;\n        let { seg } = props;\n        let timeFormat = options.eventTimeFormat || DEFAULT_TABLE_EVENT_TIME_FORMAT;\n        let timeText = buildSegTimeText(seg, timeFormat, context, true, props.defaultDisplayEventEnd);\n        return (createElement(EventContainer, Object.assign({}, props, { elTag: \"a\", elClasses: ['fc-daygrid-event', 'fc-daygrid-dot-event'], elAttrs: getSegAnchorAttrs(props.seg, context), defaultGenerator: renderInnerContent, timeText: timeText, isResizing: false, isDateSelecting: false })));\n    }\n}\nfunction renderInnerContent(renderProps) {\n    return (createElement(Fragment, null,\n        createElement(\"div\", { className: \"fc-daygrid-event-dot\", style: { borderColor: renderProps.borderColor || renderProps.backgroundColor } }),\n        renderProps.timeText && (createElement(\"div\", { className: \"fc-event-time\" }, renderProps.timeText)),\n        createElement(\"div\", { className: \"fc-event-title\" }, renderProps.event.title || createElement(Fragment, null, \"\\u00A0\"))));\n}\n\nclass TableCellMoreLink extends BaseComponent {\n    constructor() {\n        super(...arguments);\n        this.compileSegs = memoize(compileSegs);\n    }\n    render() {\n        let { props } = this;\n        let { allSegs, invisibleSegs } = this.compileSegs(props.singlePlacements);\n        return (createElement(MoreLinkContainer, { elClasses: ['fc-daygrid-more-link'], dateProfile: props.dateProfile, todayRange: props.todayRange, allDayDate: props.allDayDate, moreCnt: props.moreCnt, allSegs: allSegs, hiddenSegs: invisibleSegs, alignmentElRef: props.alignmentElRef, alignGridTop: props.alignGridTop, extraDateSpan: props.extraDateSpan, popoverContent: () => {\n                let isForcedInvisible = (props.eventDrag ? props.eventDrag.affectedInstances : null) ||\n                    (props.eventResize ? props.eventResize.affectedInstances : null) ||\n                    {};\n                return (createElement(Fragment, null, allSegs.map((seg) => {\n                    let instanceId = seg.eventRange.instance.instanceId;\n                    return (createElement(\"div\", { className: \"fc-daygrid-event-harness\", key: instanceId, style: {\n                            visibility: isForcedInvisible[instanceId] ? 'hidden' : '',\n                        } }, hasListItemDisplay(seg) ? (createElement(TableListItemEvent, Object.assign({ seg: seg, isDragging: false, isSelected: instanceId === props.eventSelection, defaultDisplayEventEnd: false }, getSegMeta(seg, props.todayRange)))) : (createElement(TableBlockEvent, Object.assign({ seg: seg, isDragging: false, isResizing: false, isDateSelecting: false, isSelected: instanceId === props.eventSelection, defaultDisplayEventEnd: false }, getSegMeta(seg, props.todayRange))))));\n                })));\n            } }));\n    }\n}\nfunction compileSegs(singlePlacements) {\n    let allSegs = [];\n    let invisibleSegs = [];\n    for (let placement of singlePlacements) {\n        allSegs.push(placement.seg);\n        if (!placement.isVisible) {\n            invisibleSegs.push(placement.seg);\n        }\n    }\n    return { allSegs, invisibleSegs };\n}\n\nconst DEFAULT_WEEK_NUM_FORMAT = createFormatter({ week: 'narrow' });\nclass TableCell extends DateComponent {\n    constructor() {\n        super(...arguments);\n        this.rootElRef = createRef();\n        this.state = {\n            dayNumberId: getUniqueDomId(),\n        };\n        this.handleRootEl = (el) => {\n            setRef(this.rootElRef, el);\n            setRef(this.props.elRef, el);\n        };\n    }\n    render() {\n        let { context, props, state, rootElRef } = this;\n        let { options, dateEnv } = context;\n        let { date, dateProfile } = props;\n        // TODO: memoize this?\n        const isMonthStart = props.showDayNumber &&\n            shouldDisplayMonthStart(date, dateProfile.currentRange, dateEnv);\n        return (createElement(DayCellContainer, { elTag: \"td\", elRef: this.handleRootEl, elClasses: [\n                'fc-daygrid-day',\n                ...(props.extraClassNames || []),\n            ], elAttrs: Object.assign(Object.assign(Object.assign({}, props.extraDataAttrs), (props.showDayNumber ? { 'aria-labelledby': state.dayNumberId } : {})), { role: 'gridcell' }), defaultGenerator: renderTopInner, date: date, dateProfile: dateProfile, todayRange: props.todayRange, showDayNumber: props.showDayNumber, isMonthStart: isMonthStart, extraRenderProps: props.extraRenderProps }, (InnerContent, renderProps) => (createElement(\"div\", { ref: props.innerElRef, className: \"fc-daygrid-day-frame fc-scrollgrid-sync-inner\", style: { minHeight: props.minHeight } },\n            props.showWeekNumber && (createElement(WeekNumberContainer, { elTag: \"a\", elClasses: ['fc-daygrid-week-number'], elAttrs: buildNavLinkAttrs(context, date, 'week'), date: date, defaultFormat: DEFAULT_WEEK_NUM_FORMAT })),\n            !renderProps.isDisabled &&\n                (props.showDayNumber || hasCustomDayCellContent(options) || props.forceDayTop) ? (createElement(\"div\", { className: \"fc-daygrid-day-top\" },\n                createElement(InnerContent, { elTag: \"a\", elClasses: [\n                        'fc-daygrid-day-number',\n                        isMonthStart && 'fc-daygrid-month-start',\n                    ], elAttrs: Object.assign(Object.assign({}, buildNavLinkAttrs(context, date)), { id: state.dayNumberId }) }))) : props.showDayNumber ? (\n            // for creating correct amount of space (see issue #7162)\n            createElement(\"div\", { className: \"fc-daygrid-day-top\", style: { visibility: 'hidden' } },\n                createElement(\"a\", { className: \"fc-daygrid-day-number\" }, \"\\u00A0\"))) : undefined,\n            createElement(\"div\", { className: \"fc-daygrid-day-events\", ref: props.fgContentElRef },\n                props.fgContent,\n                createElement(\"div\", { className: \"fc-daygrid-day-bottom\", style: { marginTop: props.moreMarginTop } },\n                    createElement(TableCellMoreLink, { allDayDate: date, singlePlacements: props.singlePlacements, moreCnt: props.moreCnt, alignmentElRef: rootElRef, alignGridTop: !props.showDayNumber, extraDateSpan: props.extraDateSpan, dateProfile: props.dateProfile, eventSelection: props.eventSelection, eventDrag: props.eventDrag, eventResize: props.eventResize, todayRange: props.todayRange }))),\n            createElement(\"div\", { className: \"fc-daygrid-day-bg\" }, props.bgContent)))));\n    }\n}\nfunction renderTopInner(props) {\n    return props.dayNumberText || createElement(Fragment, null, \"\\u00A0\");\n}\nfunction shouldDisplayMonthStart(date, currentRange, dateEnv) {\n    const { start: currentStart, end: currentEnd } = currentRange;\n    const currentEndIncl = addMs(currentEnd, -1);\n    const currentFirstYear = dateEnv.getYear(currentStart);\n    const currentFirstMonth = dateEnv.getMonth(currentStart);\n    const currentLastYear = dateEnv.getYear(currentEndIncl);\n    const currentLastMonth = dateEnv.getMonth(currentEndIncl);\n    // spans more than one month?\n    return !(currentFirstYear === currentLastYear && currentFirstMonth === currentLastMonth) &&\n        Boolean(\n        // first date in current view?\n        date.valueOf() === currentStart.valueOf() ||\n            // a month-start that's within the current range?\n            (dateEnv.getDay(date) === 1 && date.valueOf() < currentEnd.valueOf()));\n}\n\nfunction generateSegKey(seg) {\n    return seg.eventRange.instance.instanceId + ':' + seg.firstCol;\n}\nfunction generateSegUid(seg) {\n    return generateSegKey(seg) + ':' + seg.lastCol;\n}\nfunction computeFgSegPlacement(segs, // assumed already sorted\ndayMaxEvents, dayMaxEventRows, strictOrder, segHeights, maxContentHeight, cells) {\n    let hierarchy = new DayGridSegHierarchy((segEntry) => {\n        // TODO: more DRY with generateSegUid\n        let segUid = segs[segEntry.index].eventRange.instance.instanceId +\n            ':' + segEntry.span.start +\n            ':' + (segEntry.span.end - 1);\n        // if no thickness known, assume 1 (if 0, so small it always fits)\n        return segHeights[segUid] || 1;\n    });\n    hierarchy.allowReslicing = true;\n    hierarchy.strictOrder = strictOrder;\n    if (dayMaxEvents === true || dayMaxEventRows === true) {\n        hierarchy.maxCoord = maxContentHeight;\n        hierarchy.hiddenConsumes = true;\n    }\n    else if (typeof dayMaxEvents === 'number') {\n        hierarchy.maxStackCnt = dayMaxEvents;\n    }\n    else if (typeof dayMaxEventRows === 'number') {\n        hierarchy.maxStackCnt = dayMaxEventRows;\n        hierarchy.hiddenConsumes = true;\n    }\n    // create segInputs only for segs with known heights\n    let segInputs = [];\n    let unknownHeightSegs = [];\n    for (let i = 0; i < segs.length; i += 1) {\n        let seg = segs[i];\n        let segUid = generateSegUid(seg);\n        let eventHeight = segHeights[segUid];\n        if (eventHeight != null) {\n            segInputs.push({\n                index: i,\n                span: {\n                    start: seg.firstCol,\n                    end: seg.lastCol + 1,\n                },\n            });\n        }\n        else {\n            unknownHeightSegs.push(seg);\n        }\n    }\n    let hiddenEntries = hierarchy.addSegs(segInputs);\n    let segRects = hierarchy.toRects();\n    let { singleColPlacements, multiColPlacements, leftoverMargins } = placeRects(segRects, segs, cells);\n    let moreCnts = [];\n    let moreMarginTops = [];\n    // add segs with unknown heights\n    for (let seg of unknownHeightSegs) {\n        multiColPlacements[seg.firstCol].push({\n            seg,\n            isVisible: false,\n            isAbsolute: true,\n            absoluteTop: 0,\n            marginTop: 0,\n        });\n        for (let col = seg.firstCol; col <= seg.lastCol; col += 1) {\n            singleColPlacements[col].push({\n                seg: resliceSeg(seg, col, col + 1, cells),\n                isVisible: false,\n                isAbsolute: false,\n                absoluteTop: 0,\n                marginTop: 0,\n            });\n        }\n    }\n    // add the hidden entries\n    for (let col = 0; col < cells.length; col += 1) {\n        moreCnts.push(0);\n    }\n    for (let hiddenEntry of hiddenEntries) {\n        let seg = segs[hiddenEntry.index];\n        let hiddenSpan = hiddenEntry.span;\n        multiColPlacements[hiddenSpan.start].push({\n            seg: resliceSeg(seg, hiddenSpan.start, hiddenSpan.end, cells),\n            isVisible: false,\n            isAbsolute: true,\n            absoluteTop: 0,\n            marginTop: 0,\n        });\n        for (let col = hiddenSpan.start; col < hiddenSpan.end; col += 1) {\n            moreCnts[col] += 1;\n            singleColPlacements[col].push({\n                seg: resliceSeg(seg, col, col + 1, cells),\n                isVisible: false,\n                isAbsolute: false,\n                absoluteTop: 0,\n                marginTop: 0,\n            });\n        }\n    }\n    // deal with leftover margins\n    for (let col = 0; col < cells.length; col += 1) {\n        moreMarginTops.push(leftoverMargins[col]);\n    }\n    return { singleColPlacements, multiColPlacements, moreCnts, moreMarginTops };\n}\n// rects ordered by top coord, then left\nfunction placeRects(allRects, segs, cells) {\n    let rectsByEachCol = groupRectsByEachCol(allRects, cells.length);\n    let singleColPlacements = [];\n    let multiColPlacements = [];\n    let leftoverMargins = [];\n    for (let col = 0; col < cells.length; col += 1) {\n        let rects = rectsByEachCol[col];\n        // compute all static segs in singlePlacements\n        let singlePlacements = [];\n        let currentHeight = 0;\n        let currentMarginTop = 0;\n        for (let rect of rects) {\n            let seg = segs[rect.index];\n            singlePlacements.push({\n                seg: resliceSeg(seg, col, col + 1, cells),\n                isVisible: true,\n                isAbsolute: false,\n                absoluteTop: rect.levelCoord,\n                marginTop: rect.levelCoord - currentHeight,\n            });\n            currentHeight = rect.levelCoord + rect.thickness;\n        }\n        // compute mixed static/absolute segs in multiPlacements\n        let multiPlacements = [];\n        currentHeight = 0;\n        currentMarginTop = 0;\n        for (let rect of rects) {\n            let seg = segs[rect.index];\n            let isAbsolute = rect.span.end - rect.span.start > 1; // multi-column?\n            let isFirstCol = rect.span.start === col;\n            currentMarginTop += rect.levelCoord - currentHeight; // amount of space since bottom of previous seg\n            currentHeight = rect.levelCoord + rect.thickness; // height will now be bottom of current seg\n            if (isAbsolute) {\n                currentMarginTop += rect.thickness;\n                if (isFirstCol) {\n                    multiPlacements.push({\n                        seg: resliceSeg(seg, rect.span.start, rect.span.end, cells),\n                        isVisible: true,\n                        isAbsolute: true,\n                        absoluteTop: rect.levelCoord,\n                        marginTop: 0,\n                    });\n                }\n            }\n            else if (isFirstCol) {\n                multiPlacements.push({\n                    seg: resliceSeg(seg, rect.span.start, rect.span.end, cells),\n                    isVisible: true,\n                    isAbsolute: false,\n                    absoluteTop: rect.levelCoord,\n                    marginTop: currentMarginTop, // claim the margin\n                });\n                currentMarginTop = 0;\n            }\n        }\n        singleColPlacements.push(singlePlacements);\n        multiColPlacements.push(multiPlacements);\n        leftoverMargins.push(currentMarginTop);\n    }\n    return { singleColPlacements, multiColPlacements, leftoverMargins };\n}\nfunction groupRectsByEachCol(rects, colCnt) {\n    let rectsByEachCol = [];\n    for (let col = 0; col < colCnt; col += 1) {\n        rectsByEachCol.push([]);\n    }\n    for (let rect of rects) {\n        for (let col = rect.span.start; col < rect.span.end; col += 1) {\n            rectsByEachCol[col].push(rect);\n        }\n    }\n    return rectsByEachCol;\n}\nfunction resliceSeg(seg, spanStart, spanEnd, cells) {\n    if (seg.firstCol === spanStart && seg.lastCol === spanEnd - 1) {\n        return seg;\n    }\n    let eventRange = seg.eventRange;\n    let origRange = eventRange.range;\n    let slicedRange = intersectRanges(origRange, {\n        start: cells[spanStart].date,\n        end: addDays(cells[spanEnd - 1].date, 1),\n    });\n    return Object.assign(Object.assign({}, seg), { firstCol: spanStart, lastCol: spanEnd - 1, eventRange: {\n            def: eventRange.def,\n            ui: Object.assign(Object.assign({}, eventRange.ui), { durationEditable: false }),\n            instance: eventRange.instance,\n            range: slicedRange,\n        }, isStart: seg.isStart && slicedRange.start.valueOf() === origRange.start.valueOf(), isEnd: seg.isEnd && slicedRange.end.valueOf() === origRange.end.valueOf() });\n}\nclass DayGridSegHierarchy extends SegHierarchy {\n    constructor() {\n        super(...arguments);\n        // config\n        this.hiddenConsumes = false;\n        // allows us to keep hidden entries in the hierarchy so they take up space\n        this.forceHidden = {};\n    }\n    addSegs(segInputs) {\n        const hiddenSegs = super.addSegs(segInputs);\n        const { entriesByLevel } = this;\n        const excludeHidden = (entry) => !this.forceHidden[buildEntryKey(entry)];\n        // remove the forced-hidden segs\n        for (let level = 0; level < entriesByLevel.length; level += 1) {\n            entriesByLevel[level] = entriesByLevel[level].filter(excludeHidden);\n        }\n        return hiddenSegs;\n    }\n    handleInvalidInsertion(insertion, entry, hiddenEntries) {\n        const { entriesByLevel, forceHidden } = this;\n        const { touchingEntry, touchingLevel, touchingLateral } = insertion;\n        // the entry that the new insertion is touching must be hidden\n        if (this.hiddenConsumes && touchingEntry) {\n            const touchingEntryId = buildEntryKey(touchingEntry);\n            if (!forceHidden[touchingEntryId]) {\n                if (this.allowReslicing) {\n                    // split up the touchingEntry, reinsert it\n                    const hiddenEntry = Object.assign(Object.assign({}, touchingEntry), { span: intersectSpans(touchingEntry.span, entry.span) });\n                    // reinsert the area that turned into a \"more\" link (so no other entries try to\n                    // occupy the space) but mark it forced-hidden\n                    const hiddenEntryId = buildEntryKey(hiddenEntry);\n                    forceHidden[hiddenEntryId] = true;\n                    entriesByLevel[touchingLevel][touchingLateral] = hiddenEntry;\n                    hiddenEntries.push(hiddenEntry);\n                    this.splitEntry(touchingEntry, entry, hiddenEntries);\n                }\n                else {\n                    forceHidden[touchingEntryId] = true;\n                    hiddenEntries.push(touchingEntry);\n                }\n            }\n        }\n        // will try to reslice...\n        super.handleInvalidInsertion(insertion, entry, hiddenEntries);\n    }\n}\n\nclass TableRow extends DateComponent {\n    constructor() {\n        super(...arguments);\n        this.cellElRefs = new RefMap(); // the <td>\n        this.frameElRefs = new RefMap(); // the fc-daygrid-day-frame\n        this.fgElRefs = new RefMap(); // the fc-daygrid-day-events\n        this.segHarnessRefs = new RefMap(); // indexed by \"instanceId:firstCol\"\n        this.rootElRef = createRef();\n        this.state = {\n            framePositions: null,\n            maxContentHeight: null,\n            segHeights: {},\n        };\n        this.handleResize = (isForced) => {\n            if (isForced) {\n                this.updateSizing(true); // isExternal=true\n            }\n        };\n    }\n    render() {\n        let { props, state, context } = this;\n        let { options } = context;\n        let colCnt = props.cells.length;\n        let businessHoursByCol = splitSegsByFirstCol(props.businessHourSegs, colCnt);\n        let bgEventSegsByCol = splitSegsByFirstCol(props.bgEventSegs, colCnt);\n        let highlightSegsByCol = splitSegsByFirstCol(this.getHighlightSegs(), colCnt);\n        let mirrorSegsByCol = splitSegsByFirstCol(this.getMirrorSegs(), colCnt);\n        let { singleColPlacements, multiColPlacements, moreCnts, moreMarginTops } = computeFgSegPlacement(sortEventSegs(props.fgEventSegs, options.eventOrder), props.dayMaxEvents, props.dayMaxEventRows, options.eventOrderStrict, state.segHeights, state.maxContentHeight, props.cells);\n        let isForcedInvisible = // TODO: messy way to compute this\n         (props.eventDrag && props.eventDrag.affectedInstances) ||\n            (props.eventResize && props.eventResize.affectedInstances) ||\n            {};\n        return (createElement(\"tr\", { ref: this.rootElRef, role: \"row\" },\n            props.renderIntro && props.renderIntro(),\n            props.cells.map((cell, col) => {\n                let normalFgNodes = this.renderFgSegs(col, props.forPrint ? singleColPlacements[col] : multiColPlacements[col], props.todayRange, isForcedInvisible);\n                let mirrorFgNodes = this.renderFgSegs(col, buildMirrorPlacements(mirrorSegsByCol[col], multiColPlacements), props.todayRange, {}, Boolean(props.eventDrag), Boolean(props.eventResize), false);\n                return (createElement(TableCell, { key: cell.key, elRef: this.cellElRefs.createRef(cell.key), innerElRef: this.frameElRefs.createRef(cell.key) /* FF <td> problem, but okay to use for left/right. TODO: rename prop */, dateProfile: props.dateProfile, date: cell.date, showDayNumber: props.showDayNumbers, showWeekNumber: props.showWeekNumbers && col === 0, forceDayTop: props.showWeekNumbers /* even displaying weeknum for row, not necessarily day */, todayRange: props.todayRange, eventSelection: props.eventSelection, eventDrag: props.eventDrag, eventResize: props.eventResize, extraRenderProps: cell.extraRenderProps, extraDataAttrs: cell.extraDataAttrs, extraClassNames: cell.extraClassNames, extraDateSpan: cell.extraDateSpan, moreCnt: moreCnts[col], moreMarginTop: moreMarginTops[col], singlePlacements: singleColPlacements[col], fgContentElRef: this.fgElRefs.createRef(cell.key), fgContent: ( // Fragment scopes the keys\n                    createElement(Fragment, null,\n                        createElement(Fragment, null, normalFgNodes),\n                        createElement(Fragment, null, mirrorFgNodes))), bgContent: ( // Fragment scopes the keys\n                    createElement(Fragment, null,\n                        this.renderFillSegs(highlightSegsByCol[col], 'highlight'),\n                        this.renderFillSegs(businessHoursByCol[col], 'non-business'),\n                        this.renderFillSegs(bgEventSegsByCol[col], 'bg-event'))), minHeight: props.cellMinHeight }));\n            })));\n    }\n    componentDidMount() {\n        this.updateSizing(true);\n        this.context.addResizeHandler(this.handleResize);\n    }\n    componentDidUpdate(prevProps, prevState) {\n        let currentProps = this.props;\n        this.updateSizing(!isPropsEqual(prevProps, currentProps));\n    }\n    componentWillUnmount() {\n        this.context.removeResizeHandler(this.handleResize);\n    }\n    getHighlightSegs() {\n        let { props } = this;\n        if (props.eventDrag && props.eventDrag.segs.length) { // messy check\n            return props.eventDrag.segs;\n        }\n        if (props.eventResize && props.eventResize.segs.length) { // messy check\n            return props.eventResize.segs;\n        }\n        return props.dateSelectionSegs;\n    }\n    getMirrorSegs() {\n        let { props } = this;\n        if (props.eventResize && props.eventResize.segs.length) { // messy check\n            return props.eventResize.segs;\n        }\n        return [];\n    }\n    renderFgSegs(col, segPlacements, todayRange, isForcedInvisible, isDragging, isResizing, isDateSelecting) {\n        let { context } = this;\n        let { eventSelection } = this.props;\n        let { framePositions } = this.state;\n        let defaultDisplayEventEnd = this.props.cells.length === 1; // colCnt === 1\n        let isMirror = isDragging || isResizing || isDateSelecting;\n        let nodes = [];\n        if (framePositions) {\n            for (let placement of segPlacements) {\n                let { seg } = placement;\n                let { instanceId } = seg.eventRange.instance;\n                let isVisible = placement.isVisible && !isForcedInvisible[instanceId];\n                let isAbsolute = placement.isAbsolute;\n                let left = '';\n                let right = '';\n                if (isAbsolute) {\n                    if (context.isRtl) {\n                        right = 0;\n                        left = framePositions.lefts[seg.lastCol] - framePositions.lefts[seg.firstCol];\n                    }\n                    else {\n                        left = 0;\n                        right = framePositions.rights[seg.firstCol] - framePositions.rights[seg.lastCol];\n                    }\n                }\n                /*\n                known bug: events that are force to be list-item but span multiple days still take up space in later columns\n                todo: in print view, for multi-day events, don't display title within non-start/end segs\n                */\n                nodes.push(createElement(\"div\", { className: 'fc-daygrid-event-harness' + (isAbsolute ? ' fc-daygrid-event-harness-abs' : ''), key: generateSegKey(seg), ref: isMirror ? null : this.segHarnessRefs.createRef(generateSegUid(seg)), style: {\n                        visibility: isVisible ? '' : 'hidden',\n                        marginTop: isAbsolute ? '' : placement.marginTop,\n                        top: isAbsolute ? placement.absoluteTop : '',\n                        left,\n                        right,\n                    } }, hasListItemDisplay(seg) ? (createElement(TableListItemEvent, Object.assign({ seg: seg, isDragging: isDragging, isSelected: instanceId === eventSelection, defaultDisplayEventEnd: defaultDisplayEventEnd }, getSegMeta(seg, todayRange)))) : (createElement(TableBlockEvent, Object.assign({ seg: seg, isDragging: isDragging, isResizing: isResizing, isDateSelecting: isDateSelecting, isSelected: instanceId === eventSelection, defaultDisplayEventEnd: defaultDisplayEventEnd }, getSegMeta(seg, todayRange))))));\n            }\n        }\n        return nodes;\n    }\n    renderFillSegs(segs, fillType) {\n        let { isRtl } = this.context;\n        let { todayRange } = this.props;\n        let { framePositions } = this.state;\n        let nodes = [];\n        if (framePositions) {\n            for (let seg of segs) {\n                let leftRightCss = isRtl ? {\n                    right: 0,\n                    left: framePositions.lefts[seg.lastCol] - framePositions.lefts[seg.firstCol],\n                } : {\n                    left: 0,\n                    right: framePositions.rights[seg.firstCol] - framePositions.rights[seg.lastCol],\n                };\n                nodes.push(createElement(\"div\", { key: buildEventRangeKey(seg.eventRange), className: \"fc-daygrid-bg-harness\", style: leftRightCss }, fillType === 'bg-event' ?\n                    createElement(BgEvent, Object.assign({ seg: seg }, getSegMeta(seg, todayRange))) :\n                    renderFill(fillType)));\n            }\n        }\n        return createElement(Fragment, {}, ...nodes);\n    }\n    updateSizing(isExternalSizingChange) {\n        let { props, state, frameElRefs } = this;\n        if (!props.forPrint &&\n            props.clientWidth !== null // positioning ready?\n        ) {\n            if (isExternalSizingChange) {\n                let frameEls = props.cells.map((cell) => frameElRefs.currentMap[cell.key]);\n                if (frameEls.length) {\n                    let originEl = this.rootElRef.current;\n                    let newPositionCache = new PositionCache(originEl, frameEls, true, // isHorizontal\n                    false);\n                    if (!state.framePositions || !state.framePositions.similarTo(newPositionCache)) {\n                        this.setState({\n                            framePositions: new PositionCache(originEl, frameEls, true, // isHorizontal\n                            false),\n                        });\n                    }\n                }\n            }\n            const oldSegHeights = this.state.segHeights;\n            const newSegHeights = this.querySegHeights();\n            const limitByContentHeight = props.dayMaxEvents === true || props.dayMaxEventRows === true;\n            this.safeSetState({\n                // HACK to prevent oscillations of events being shown/hidden from max-event-rows\n                // Essentially, once you compute an element's height, never null-out.\n                // TODO: always display all events, as visibility:hidden?\n                segHeights: Object.assign(Object.assign({}, oldSegHeights), newSegHeights),\n                maxContentHeight: limitByContentHeight ? this.computeMaxContentHeight() : null,\n            });\n        }\n    }\n    querySegHeights() {\n        let segElMap = this.segHarnessRefs.currentMap;\n        let segHeights = {};\n        // get the max height amongst instance segs\n        for (let segUid in segElMap) {\n            let height = Math.round(segElMap[segUid].getBoundingClientRect().height);\n            segHeights[segUid] = Math.max(segHeights[segUid] || 0, height);\n        }\n        return segHeights;\n    }\n    computeMaxContentHeight() {\n        let firstKey = this.props.cells[0].key;\n        let cellEl = this.cellElRefs.currentMap[firstKey];\n        let fcContainerEl = this.fgElRefs.currentMap[firstKey];\n        return cellEl.getBoundingClientRect().bottom - fcContainerEl.getBoundingClientRect().top;\n    }\n    getCellEls() {\n        let elMap = this.cellElRefs.currentMap;\n        return this.props.cells.map((cell) => elMap[cell.key]);\n    }\n}\nTableRow.addStateEquality({\n    segHeights: isPropsEqual,\n});\nfunction buildMirrorPlacements(mirrorSegs, colPlacements) {\n    if (!mirrorSegs.length) {\n        return [];\n    }\n    let topsByInstanceId = buildAbsoluteTopHash(colPlacements); // TODO: cache this at first render?\n    return mirrorSegs.map((seg) => ({\n        seg,\n        isVisible: true,\n        isAbsolute: true,\n        absoluteTop: topsByInstanceId[seg.eventRange.instance.instanceId],\n        marginTop: 0,\n    }));\n}\nfunction buildAbsoluteTopHash(colPlacements) {\n    let topsByInstanceId = {};\n    for (let placements of colPlacements) {\n        for (let placement of placements) {\n            topsByInstanceId[placement.seg.eventRange.instance.instanceId] = placement.absoluteTop;\n        }\n    }\n    return topsByInstanceId;\n}\n\nclass TableRows extends DateComponent {\n    constructor() {\n        super(...arguments);\n        this.splitBusinessHourSegs = memoize(splitSegsByRow);\n        this.splitBgEventSegs = memoize(splitSegsByRow);\n        this.splitFgEventSegs = memoize(splitSegsByRow);\n        this.splitDateSelectionSegs = memoize(splitSegsByRow);\n        this.splitEventDrag = memoize(splitInteractionByRow);\n        this.splitEventResize = memoize(splitInteractionByRow);\n        this.rowRefs = new RefMap();\n    }\n    render() {\n        let { props, context } = this;\n        let rowCnt = props.cells.length;\n        let businessHourSegsByRow = this.splitBusinessHourSegs(props.businessHourSegs, rowCnt);\n        let bgEventSegsByRow = this.splitBgEventSegs(props.bgEventSegs, rowCnt);\n        let fgEventSegsByRow = this.splitFgEventSegs(props.fgEventSegs, rowCnt);\n        let dateSelectionSegsByRow = this.splitDateSelectionSegs(props.dateSelectionSegs, rowCnt);\n        let eventDragByRow = this.splitEventDrag(props.eventDrag, rowCnt);\n        let eventResizeByRow = this.splitEventResize(props.eventResize, rowCnt);\n        // for DayGrid view with many rows, force a min-height on cells so doesn't appear squished\n        // choose 7 because a month view will have max 6 rows\n        let cellMinHeight = (rowCnt >= 7 && props.clientWidth) ?\n            props.clientWidth / context.options.aspectRatio / 6 :\n            null;\n        return (createElement(NowTimer, { unit: \"day\" }, (nowDate, todayRange) => (createElement(Fragment, null, props.cells.map((cells, row) => (createElement(TableRow, { ref: this.rowRefs.createRef(row), key: cells.length\n                ? cells[0].date.toISOString() /* best? or put key on cell? or use diff formatter? */\n                : row // in case there are no cells (like when resource view is loading)\n            , showDayNumbers: rowCnt > 1, showWeekNumbers: props.showWeekNumbers, todayRange: todayRange, dateProfile: props.dateProfile, cells: cells, renderIntro: props.renderRowIntro, businessHourSegs: businessHourSegsByRow[row], eventSelection: props.eventSelection, bgEventSegs: bgEventSegsByRow[row].filter(isSegAllDay) /* hack */, fgEventSegs: fgEventSegsByRow[row], dateSelectionSegs: dateSelectionSegsByRow[row], eventDrag: eventDragByRow[row], eventResize: eventResizeByRow[row], dayMaxEvents: props.dayMaxEvents, dayMaxEventRows: props.dayMaxEventRows, clientWidth: props.clientWidth, clientHeight: props.clientHeight, cellMinHeight: cellMinHeight, forPrint: props.forPrint })))))));\n    }\n    componentDidMount() {\n        this.registerInteractiveComponent();\n    }\n    componentDidUpdate() {\n        // for if started with zero cells\n        this.registerInteractiveComponent();\n    }\n    registerInteractiveComponent() {\n        if (!this.rootEl) {\n            // HACK: need a daygrid wrapper parent to do positioning\n            // NOTE: a daygrid resource view w/o resources can have zero cells\n            const firstCellEl = this.rowRefs.currentMap[0].getCellEls()[0];\n            const rootEl = firstCellEl ? firstCellEl.closest('.fc-daygrid-body') : null;\n            if (rootEl) {\n                this.rootEl = rootEl;\n                this.context.registerInteractiveComponent(this, {\n                    el: rootEl,\n                    isHitComboAllowed: this.props.isHitComboAllowed,\n                });\n            }\n        }\n    }\n    componentWillUnmount() {\n        if (this.rootEl) {\n            this.context.unregisterInteractiveComponent(this);\n            this.rootEl = null;\n        }\n    }\n    // Hit System\n    // ----------------------------------------------------------------------------------------------------\n    prepareHits() {\n        this.rowPositions = new PositionCache(this.rootEl, this.rowRefs.collect().map((rowObj) => rowObj.getCellEls()[0]), // first cell el in each row. TODO: not optimal\n        false, true);\n        this.colPositions = new PositionCache(this.rootEl, this.rowRefs.currentMap[0].getCellEls(), // cell els in first row\n        true, // horizontal\n        false);\n    }\n    queryHit(positionLeft, positionTop) {\n        let { colPositions, rowPositions } = this;\n        let col = colPositions.leftToIndex(positionLeft);\n        let row = rowPositions.topToIndex(positionTop);\n        if (row != null && col != null) {\n            let cell = this.props.cells[row][col];\n            return {\n                dateProfile: this.props.dateProfile,\n                dateSpan: Object.assign({ range: this.getCellRange(row, col), allDay: true }, cell.extraDateSpan),\n                dayEl: this.getCellEl(row, col),\n                rect: {\n                    left: colPositions.lefts[col],\n                    right: colPositions.rights[col],\n                    top: rowPositions.tops[row],\n                    bottom: rowPositions.bottoms[row],\n                },\n                layer: 0,\n            };\n        }\n        return null;\n    }\n    getCellEl(row, col) {\n        return this.rowRefs.currentMap[row].getCellEls()[col]; // TODO: not optimal\n    }\n    getCellRange(row, col) {\n        let start = this.props.cells[row][col].date;\n        let end = addDays(start, 1);\n        return { start, end };\n    }\n}\nfunction isSegAllDay(seg) {\n    return seg.eventRange.def.allDay;\n}\n\nclass Table extends DateComponent {\n    constructor() {\n        super(...arguments);\n        this.elRef = createRef();\n        this.needsScrollReset = false;\n    }\n    render() {\n        let { props } = this;\n        let { dayMaxEventRows, dayMaxEvents, expandRows } = props;\n        let limitViaBalanced = dayMaxEvents === true || dayMaxEventRows === true;\n        // if rows can't expand to fill fixed height, can't do balanced-height event limit\n        // TODO: best place to normalize these options?\n        if (limitViaBalanced && !expandRows) {\n            limitViaBalanced = false;\n            dayMaxEventRows = null;\n            dayMaxEvents = null;\n        }\n        let classNames = [\n            'fc-daygrid-body',\n            limitViaBalanced ? 'fc-daygrid-body-balanced' : 'fc-daygrid-body-unbalanced',\n            expandRows ? '' : 'fc-daygrid-body-natural', // will height of one row depend on the others?\n        ];\n        return (createElement(\"div\", { ref: this.elRef, className: classNames.join(' '), style: {\n                // these props are important to give this wrapper correct dimensions for interactions\n                // TODO: if we set it here, can we avoid giving to inner tables?\n                width: props.clientWidth,\n                minWidth: props.tableMinWidth,\n            } },\n            createElement(\"table\", { role: \"presentation\", className: \"fc-scrollgrid-sync-table\", style: {\n                    width: props.clientWidth,\n                    minWidth: props.tableMinWidth,\n                    height: expandRows ? props.clientHeight : '',\n                } },\n                props.colGroupNode,\n                createElement(\"tbody\", { role: \"presentation\" },\n                    createElement(TableRows, { dateProfile: props.dateProfile, cells: props.cells, renderRowIntro: props.renderRowIntro, showWeekNumbers: props.showWeekNumbers, clientWidth: props.clientWidth, clientHeight: props.clientHeight, businessHourSegs: props.businessHourSegs, bgEventSegs: props.bgEventSegs, fgEventSegs: props.fgEventSegs, dateSelectionSegs: props.dateSelectionSegs, eventSelection: props.eventSelection, eventDrag: props.eventDrag, eventResize: props.eventResize, dayMaxEvents: dayMaxEvents, dayMaxEventRows: dayMaxEventRows, forPrint: props.forPrint, isHitComboAllowed: props.isHitComboAllowed })))));\n    }\n    componentDidMount() {\n        this.requestScrollReset();\n    }\n    componentDidUpdate(prevProps) {\n        if (prevProps.dateProfile !== this.props.dateProfile) {\n            this.requestScrollReset();\n        }\n        else {\n            this.flushScrollReset();\n        }\n    }\n    requestScrollReset() {\n        this.needsScrollReset = true;\n        this.flushScrollReset();\n    }\n    flushScrollReset() {\n        if (this.needsScrollReset &&\n            this.props.clientWidth // sizes computed?\n        ) {\n            const subjectEl = getScrollSubjectEl(this.elRef.current, this.props.dateProfile);\n            if (subjectEl) {\n                const originEl = subjectEl.closest('.fc-daygrid-body');\n                const scrollEl = originEl.closest('.fc-scroller');\n                const scrollTop = subjectEl.getBoundingClientRect().top -\n                    originEl.getBoundingClientRect().top;\n                scrollEl.scrollTop = scrollTop ? (scrollTop + 1) : 0; // overcome border\n            }\n            this.needsScrollReset = false;\n        }\n    }\n}\nfunction getScrollSubjectEl(containerEl, dateProfile) {\n    let el;\n    if (dateProfile.currentRangeUnit.match(/year|month/)) {\n        el = containerEl.querySelector(`[data-date=\"${formatIsoMonthStr(dateProfile.currentDate)}-01\"]`);\n        // even if view is month-based, first-of-month might be hidden...\n    }\n    if (!el) {\n        el = containerEl.querySelector(`[data-date=\"${formatDayString(dateProfile.currentDate)}\"]`);\n        // could still be hidden if an interior-view hidden day\n    }\n    return el;\n}\n\nclass DayTableSlicer extends Slicer {\n    constructor() {\n        super(...arguments);\n        this.forceDayIfListItem = true;\n    }\n    sliceRange(dateRange, dayTableModel) {\n        return dayTableModel.sliceRange(dateRange);\n    }\n}\n\nclass DayTable extends DateComponent {\n    constructor() {\n        super(...arguments);\n        this.slicer = new DayTableSlicer();\n        this.tableRef = createRef();\n    }\n    render() {\n        let { props, context } = this;\n        return (createElement(Table, Object.assign({ ref: this.tableRef }, this.slicer.sliceProps(props, props.dateProfile, props.nextDayThreshold, context, props.dayTableModel), { dateProfile: props.dateProfile, cells: props.dayTableModel.cells, colGroupNode: props.colGroupNode, tableMinWidth: props.tableMinWidth, renderRowIntro: props.renderRowIntro, dayMaxEvents: props.dayMaxEvents, dayMaxEventRows: props.dayMaxEventRows, showWeekNumbers: props.showWeekNumbers, expandRows: props.expandRows, headerAlignElRef: props.headerAlignElRef, clientWidth: props.clientWidth, clientHeight: props.clientHeight, forPrint: props.forPrint })));\n    }\n}\n\nclass DayTableView extends TableView {\n    constructor() {\n        super(...arguments);\n        this.buildDayTableModel = memoize(buildDayTableModel);\n        this.headerRef = createRef();\n        this.tableRef = createRef();\n        // can't override any lifecycle methods from parent\n    }\n    render() {\n        let { options, dateProfileGenerator } = this.context;\n        let { props } = this;\n        let dayTableModel = this.buildDayTableModel(props.dateProfile, dateProfileGenerator);\n        let headerContent = options.dayHeaders && (createElement(DayHeader, { ref: this.headerRef, dateProfile: props.dateProfile, dates: dayTableModel.headerDates, datesRepDistinctDays: dayTableModel.rowCnt === 1 }));\n        let bodyContent = (contentArg) => (createElement(DayTable, { ref: this.tableRef, dateProfile: props.dateProfile, dayTableModel: dayTableModel, businessHours: props.businessHours, dateSelection: props.dateSelection, eventStore: props.eventStore, eventUiBases: props.eventUiBases, eventSelection: props.eventSelection, eventDrag: props.eventDrag, eventResize: props.eventResize, nextDayThreshold: options.nextDayThreshold, colGroupNode: contentArg.tableColGroupNode, tableMinWidth: contentArg.tableMinWidth, dayMaxEvents: options.dayMaxEvents, dayMaxEventRows: options.dayMaxEventRows, showWeekNumbers: options.weekNumbers, expandRows: !props.isHeightAuto, headerAlignElRef: this.headerElRef, clientWidth: contentArg.clientWidth, clientHeight: contentArg.clientHeight, forPrint: props.forPrint }));\n        return options.dayMinWidth\n            ? this.renderHScrollLayout(headerContent, bodyContent, dayTableModel.colCnt, options.dayMinWidth)\n            : this.renderSimpleLayout(headerContent, bodyContent);\n    }\n}\nfunction buildDayTableModel(dateProfile, dateProfileGenerator) {\n    let daySeries = new DaySeriesModel(dateProfile.renderRange, dateProfileGenerator);\n    return new DayTableModel(daySeries, /year|month|week/.test(dateProfile.currentRangeUnit));\n}\n\nclass TableDateProfileGenerator extends DateProfileGenerator {\n    // Computes the date range that will be rendered\n    buildRenderRange(currentRange, currentRangeUnit, isRangeAllDay) {\n        let renderRange = super.buildRenderRange(currentRange, currentRangeUnit, isRangeAllDay);\n        let { props } = this;\n        return buildDayTableRenderRange({\n            currentRange: renderRange,\n            snapToWeek: /^(year|month)$/.test(currentRangeUnit),\n            fixedWeekCount: props.fixedWeekCount,\n            dateEnv: props.dateEnv,\n        });\n    }\n}\nfunction buildDayTableRenderRange(props) {\n    let { dateEnv, currentRange } = props;\n    let { start, end } = currentRange;\n    let endOfWeek;\n    // year and month views should be aligned with weeks. this is already done for week\n    if (props.snapToWeek) {\n        start = dateEnv.startOfWeek(start);\n        // make end-of-week if not already\n        endOfWeek = dateEnv.startOfWeek(end);\n        if (endOfWeek.valueOf() !== end.valueOf()) {\n            end = addWeeks(endOfWeek, 1);\n        }\n    }\n    // ensure 6 weeks\n    if (props.fixedWeekCount) {\n        // TODO: instead of these date-math gymnastics (for multimonth view),\n        // compute dateprofiles of all months, then use start of first and end of last.\n        let lastMonthRenderStart = dateEnv.startOfWeek(dateEnv.startOfMonth(addDays(currentRange.end, -1)));\n        let rowCnt = Math.ceil(// could be partial weeks due to hiddenDays\n        diffWeeks(lastMonthRenderStart, end));\n        end = addWeeks(end, 6 - rowCnt);\n    }\n    return { start, end };\n}\n\nvar css_248z = \":root{--fc-daygrid-event-dot-width:8px}.fc-daygrid-day-events:after,.fc-daygrid-day-events:before,.fc-daygrid-day-frame:after,.fc-daygrid-day-frame:before,.fc-daygrid-event-harness:after,.fc-daygrid-event-harness:before{clear:both;content:\\\"\\\";display:table}.fc .fc-daygrid-body{position:relative;z-index:1}.fc .fc-daygrid-day.fc-day-today{background-color:var(--fc-today-bg-color)}.fc .fc-daygrid-day-frame{min-height:100%;position:relative}.fc .fc-daygrid-day-top{display:flex;flex-direction:row-reverse}.fc .fc-day-other .fc-daygrid-day-top{opacity:.3}.fc .fc-daygrid-day-number{padding:4px;position:relative;z-index:4}.fc .fc-daygrid-month-start{font-size:1.1em;font-weight:700}.fc .fc-daygrid-day-events{margin-top:1px}.fc .fc-daygrid-body-balanced .fc-daygrid-day-events{left:0;position:absolute;right:0}.fc .fc-daygrid-body-unbalanced .fc-daygrid-day-events{min-height:2em;position:relative}.fc .fc-daygrid-body-natural .fc-daygrid-day-events{margin-bottom:1em}.fc .fc-daygrid-event-harness{position:relative}.fc .fc-daygrid-event-harness-abs{left:0;position:absolute;right:0;top:0}.fc .fc-daygrid-bg-harness{bottom:0;position:absolute;top:0}.fc .fc-daygrid-day-bg .fc-non-business{z-index:1}.fc .fc-daygrid-day-bg .fc-bg-event{z-index:2}.fc .fc-daygrid-day-bg .fc-highlight{z-index:3}.fc .fc-daygrid-event{margin-top:1px;z-index:6}.fc .fc-daygrid-event.fc-event-mirror{z-index:7}.fc .fc-daygrid-day-bottom{font-size:.85em;margin:0 2px}.fc .fc-daygrid-day-bottom:after,.fc .fc-daygrid-day-bottom:before{clear:both;content:\\\"\\\";display:table}.fc .fc-daygrid-more-link{border-radius:3px;cursor:pointer;line-height:1;margin-top:1px;max-width:100%;overflow:hidden;padding:2px;position:relative;white-space:nowrap;z-index:4}.fc .fc-daygrid-more-link:hover{background-color:rgba(0,0,0,.1)}.fc .fc-daygrid-week-number{background-color:var(--fc-neutral-bg-color);color:var(--fc-neutral-text-color);min-width:1.5em;padding:2px;position:absolute;text-align:center;top:0;z-index:5}.fc .fc-more-popover .fc-popover-body{min-width:220px;padding:10px}.fc-direction-ltr .fc-daygrid-event.fc-event-start,.fc-direction-rtl .fc-daygrid-event.fc-event-end{margin-left:2px}.fc-direction-ltr .fc-daygrid-event.fc-event-end,.fc-direction-rtl .fc-daygrid-event.fc-event-start{margin-right:2px}.fc-direction-ltr .fc-daygrid-more-link{float:left}.fc-direction-ltr .fc-daygrid-week-number{border-radius:0 0 3px 0;left:0}.fc-direction-rtl .fc-daygrid-more-link{float:right}.fc-direction-rtl .fc-daygrid-week-number{border-radius:0 0 0 3px;right:0}.fc-liquid-hack .fc-daygrid-day-frame{position:static}.fc-daygrid-event{border-radius:3px;font-size:var(--fc-small-font-size);position:relative;white-space:nowrap}.fc-daygrid-block-event .fc-event-time{font-weight:700}.fc-daygrid-block-event .fc-event-time,.fc-daygrid-block-event .fc-event-title{padding:1px}.fc-daygrid-dot-event{align-items:center;display:flex;padding:2px 0}.fc-daygrid-dot-event .fc-event-title{flex-grow:1;flex-shrink:1;font-weight:700;min-width:0;overflow:hidden}.fc-daygrid-dot-event.fc-event-mirror,.fc-daygrid-dot-event:hover{background:rgba(0,0,0,.1)}.fc-daygrid-dot-event.fc-event-selected:before{bottom:-10px;top:-10px}.fc-daygrid-event-dot{border:calc(var(--fc-daygrid-event-dot-width)/2) solid var(--fc-event-border-color);border-radius:calc(var(--fc-daygrid-event-dot-width)/2);box-sizing:content-box;height:0;margin:0 4px;width:0}.fc-direction-ltr .fc-daygrid-event .fc-event-time{margin-right:3px}.fc-direction-rtl .fc-daygrid-event .fc-event-time{margin-left:3px}\";\ninjectStyles(css_248z);\n\nexport { DayTableView as DayGridView, DayTable, DayTableSlicer, Table, TableDateProfileGenerator, TableRows, TableView, buildDayTableModel, buildDayTableRenderRange };\n", "import { createPlugin } from '@fullcalendar/core/index.js';\nimport { DayGridView as DayTableView, TableDateProfileGenerator } from './internal.js';\nimport '@fullcalendar/core/internal.js';\nimport '@fullcalendar/core/preact.js';\n\nvar index = createPlugin({\n    name: '@fullcalendar/daygrid',\n    initialView: 'dayGridMonth',\n    views: {\n        dayGrid: {\n            component: DayTableView,\n            dateProfileGeneratorClass: TableDateProfileGenerator,\n        },\n        dayGridDay: {\n            type: 'dayGrid',\n            duration: { days: 1 },\n        },\n        dayGridWeek: {\n            type: 'dayGrid',\n            duration: { weeks: 1 },\n        },\n        dayGridMonth: {\n            type: 'dayGrid',\n            duration: { months: 1 },\n            fixedWeekCount: true,\n        },\n        dayGridYear: {\n            type: 'dayGrid',\n            duration: { years: 1 },\n        },\n    },\n});\n\nexport { index as default };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,IAAM,YAAN,cAAwB,cAAc;AAAA,EAClC,cAAc;AACV,UAAM,GAAG,SAAS;AAClB,SAAK,cAAc,EAAU;AAAA,EACjC;AAAA,EACA,mBAAmB,kBAAkB,aAAa;AAC9C,QAAI,EAAE,OAAO,QAAQ,IAAI;AACzB,QAAI,WAAW,CAAC;AAChB,QAAI,oBAAoB,qBAAqB,QAAQ,OAAO;AAC5D,QAAI,kBAAkB;AAClB,eAAS,KAAK;AAAA,QACV,MAAM;AAAA,QACN,KAAK;AAAA,QACL,UAAU;AAAA,QACV,OAAO;AAAA,UACH,OAAO,KAAK;AAAA,UACZ,gBAAgB;AAAA,UAChB,YAAY;AAAA,QAChB;AAAA,MACJ,CAAC;AAAA,IACL;AACA,aAAS,KAAK;AAAA,MACV,MAAM;AAAA,MACN,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,OAAO,EAAE,SAAS,YAAY;AAAA,IAClC,CAAC;AACD,WAAQ;AAAA,MAAc;AAAA,MAAe,EAAE,WAAW,CAAC,YAAY,GAAG,UAAU,QAAQ,SAAS;AAAA,MACzF,EAAc,kBAAkB,EAAE,QAAQ,CAAC,MAAM,gBAAgB,CAAC,MAAM,UAAU,kBAAkB,MAAM,UAAU,MAAM,CAAC,GAA8B,SAAmB,CAAC;AAAA,IAAC;AAAA,EACtL;AAAA,EACA,oBAAoB,kBAAkB,aAAa,QAAQ,aAAa;AACpE,QAAI,aAAa,KAAK,QAAQ,YAAY;AAC1C,QAAI,CAAC,YAAY;AACb,YAAM,IAAI,MAAM,8BAA8B;AAAA,IAClD;AACA,QAAI,EAAE,OAAO,QAAQ,IAAI;AACzB,QAAI,oBAAoB,CAAC,MAAM,YAAY,qBAAqB,QAAQ,OAAO;AAC/E,QAAI,wBAAwB,CAAC,MAAM,YAAY,yBAAyB,QAAQ,OAAO;AACvF,QAAI,WAAW,CAAC;AAChB,QAAI,kBAAkB;AAClB,eAAS,KAAK;AAAA,QACV,MAAM;AAAA,QACN,KAAK;AAAA,QACL,UAAU;AAAA,QACV,QAAQ,CAAC;AAAA,UACD,KAAK;AAAA,UACL,OAAO,KAAK;AAAA,UACZ,gBAAgB;AAAA,UAChB,YAAY;AAAA,QAChB,CAAC;AAAA,MACT,CAAC;AAAA,IACL;AACA,aAAS,KAAK;AAAA,MACV,MAAM;AAAA,MACN,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,QAAQ,CAAC;AAAA,QACD,KAAK;AAAA,QACL,SAAS;AAAA,MACb,CAAC;AAAA,IACT,CAAC;AACD,QAAI,uBAAuB;AACvB,eAAS,KAAK;AAAA,QACV,MAAM;AAAA,QACN,KAAK;AAAA,QACL,UAAU;AAAA,QACV,QAAQ,CAAC;AAAA,UACD,KAAK;AAAA,UACL,SAAS;AAAA,QACb,CAAC;AAAA,MACT,CAAC;AAAA,IACL;AACA,WAAQ;AAAA,MAAc;AAAA,MAAe,EAAE,WAAW,CAAC,YAAY,GAAG,UAAU,QAAQ,SAAS;AAAA,MACzF,EAAc,YAAY,EAAE,QAAQ,CAAC,MAAM,gBAAgB,CAAC,MAAM,UAAU,UAAU,MAAM,UAAU,kBAAkB,MAAM,UAAU,WAAW,CAAC,EAAE,MAAM,CAAC,EAAE,MAAM,QAAQ,UAAU,YAAY,CAAC,EAAE,CAAC,GAAG,SAAmB,CAAC;AAAA,IAAC;AAAA,EACvO;AACJ;AAEA,SAAS,eAAe,MAAM,QAAQ;AAClC,MAAI,QAAQ,CAAC;AACb,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK,GAAG;AAChC,UAAM,CAAC,IAAI,CAAC;AAAA,EAChB;AACA,WAAS,OAAO,MAAM;AAClB,UAAM,IAAI,GAAG,EAAE,KAAK,GAAG;AAAA,EAC3B;AACA,SAAO;AACX;AACA,SAAS,oBAAoB,MAAM,QAAQ;AACvC,MAAI,QAAQ,CAAC;AACb,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK,GAAG;AAChC,UAAM,CAAC,IAAI,CAAC;AAAA,EAChB;AACA,WAAS,OAAO,MAAM;AAClB,UAAM,IAAI,QAAQ,EAAE,KAAK,GAAG;AAAA,EAChC;AACA,SAAO;AACX;AACA,SAAS,sBAAsB,IAAI,QAAQ;AACvC,MAAI,QAAQ,CAAC;AACb,MAAI,CAAC,IAAI;AACL,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK,GAAG;AAChC,YAAM,CAAC,IAAI;AAAA,IACf;AAAA,EACJ,OACK;AACD,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK,GAAG;AAChC,YAAM,CAAC,IAAI;AAAA,QACP,mBAAmB,GAAG;AAAA,QACtB,SAAS,GAAG;AAAA,QACZ,MAAM,CAAC;AAAA,MACX;AAAA,IACJ;AACA,aAAS,OAAO,GAAG,MAAM;AACrB,YAAM,IAAI,GAAG,EAAE,KAAK,KAAK,GAAG;AAAA,IAChC;AAAA,EACJ;AACA,SAAO;AACX;AAEA,IAAM,kCAAkC,gBAAgB;AAAA,EACpD,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,gBAAgB;AAAA,EAChB,UAAU;AACd,CAAC;AACD,SAAS,mBAAmB,KAAK;AAC7B,MAAI,EAAE,QAAQ,IAAI,IAAI,WAAW;AACjC,SAAO,YAAY,eAAgB,YAAY,UAC3C,CAAC,IAAI,WAAW,IAAI,UACpB,IAAI,aAAa,IAAI;AAAA,EACrB,IAAI;AAAA,EACJ,IAAI;AAEZ;AAEA,IAAM,kBAAN,cAA8B,cAAc;AAAA,EACxC,SAAS;AACL,QAAI,EAAE,MAAM,IAAI;AAChB,WAAQ,EAAc,eAAe,OAAO,OAAO,CAAC,GAAG,OAAO,EAAE,WAAW,CAAC,oBAAoB,0BAA0B,YAAY,GAAG,mBAAmB,iCAAiC,wBAAwB,MAAM,wBAAwB,iBAAiB,CAAC,MAAM,IAAI,WAAW,IAAI,OAAO,CAAC,CAAC;AAAA,EAC3S;AACJ;AAEA,IAAM,qBAAN,cAAiC,cAAc;AAAA,EAC3C,SAAS;AACL,QAAI,EAAE,OAAO,QAAQ,IAAI;AACzB,QAAI,EAAE,QAAQ,IAAI;AAClB,QAAI,EAAE,IAAI,IAAI;AACd,QAAI,aAAa,QAAQ,mBAAmB;AAC5C,QAAI,WAAW,iBAAiB,KAAK,YAAY,SAAS,MAAM,MAAM,sBAAsB;AAC5F,WAAQ,EAAc,gBAAgB,OAAO,OAAO,CAAC,GAAG,OAAO,EAAE,OAAO,KAAK,WAAW,CAAC,oBAAoB,sBAAsB,GAAG,SAAS,kBAAkB,MAAM,KAAK,OAAO,GAAG,kBAAkB,oBAAoB,UAAoB,YAAY,OAAO,iBAAiB,MAAM,CAAC,CAAC;AAAA,EAChS;AACJ;AACA,SAAS,mBAAmB,aAAa;AACrC,SAAQ;AAAA,IAAc;AAAA,IAAU;AAAA,IAC5B,EAAc,OAAO,EAAE,WAAW,wBAAwB,OAAO,EAAE,aAAa,YAAY,eAAe,YAAY,gBAAgB,EAAE,CAAC;AAAA,IAC1I,YAAY,YAAa,EAAc,OAAO,EAAE,WAAW,gBAAgB,GAAG,YAAY,QAAQ;AAAA,IAClG,EAAc,OAAO,EAAE,WAAW,iBAAiB,GAAG,YAAY,MAAM,SAAS,EAAc,GAAU,MAAM,GAAQ,CAAC;AAAA,EAAC;AACjI;AAEA,IAAM,oBAAN,cAAgC,cAAc;AAAA,EAC1C,cAAc;AACV,UAAM,GAAG,SAAS;AAClB,SAAK,cAAc,QAAQ,WAAW;AAAA,EAC1C;AAAA,EACA,SAAS;AACL,QAAI,EAAE,MAAM,IAAI;AAChB,QAAI,EAAE,SAAS,cAAc,IAAI,KAAK,YAAY,MAAM,gBAAgB;AACxE,WAAQ,EAAc,mBAAmB,EAAE,WAAW,CAAC,sBAAsB,GAAG,aAAa,MAAM,aAAa,YAAY,MAAM,YAAY,YAAY,MAAM,YAAY,SAAS,MAAM,SAAS,SAAkB,YAAY,eAAe,gBAAgB,MAAM,gBAAgB,cAAc,MAAM,cAAc,eAAe,MAAM,eAAe,gBAAgB,MAAM;AAC3W,UAAI,qBAAqB,MAAM,YAAY,MAAM,UAAU,oBAAoB,UAC1E,MAAM,cAAc,MAAM,YAAY,oBAAoB,SAC3D,CAAC;AACL,aAAQ,EAAc,GAAU,MAAM,QAAQ,IAAI,CAAC,QAAQ;AACvD,YAAI,aAAa,IAAI,WAAW,SAAS;AACzC,eAAQ,EAAc,OAAO,EAAE,WAAW,4BAA4B,KAAK,YAAY,OAAO;AAAA,UACtF,YAAY,kBAAkB,UAAU,IAAI,WAAW;AAAA,QAC3D,EAAE,GAAG,mBAAmB,GAAG,IAAK,EAAc,oBAAoB,OAAO,OAAO,EAAE,KAAU,YAAY,OAAO,YAAY,eAAe,MAAM,gBAAgB,wBAAwB,MAAM,GAAG,WAAW,KAAK,MAAM,UAAU,CAAC,CAAC,IAAM,EAAc,iBAAiB,OAAO,OAAO,EAAE,KAAU,YAAY,OAAO,YAAY,OAAO,iBAAiB,OAAO,YAAY,eAAe,MAAM,gBAAgB,wBAAwB,MAAM,GAAG,WAAW,KAAK,MAAM,UAAU,CAAC,CAAC,CAAE;AAAA,MAC9d,CAAC,CAAC;AAAA,IACN,EAAE,CAAC;AAAA,EACX;AACJ;AACA,SAAS,YAAY,kBAAkB;AACnC,MAAI,UAAU,CAAC;AACf,MAAI,gBAAgB,CAAC;AACrB,WAAS,aAAa,kBAAkB;AACpC,YAAQ,KAAK,UAAU,GAAG;AAC1B,QAAI,CAAC,UAAU,WAAW;AACtB,oBAAc,KAAK,UAAU,GAAG;AAAA,IACpC;AAAA,EACJ;AACA,SAAO,EAAE,SAAS,cAAc;AACpC;AAEA,IAAM,0BAA0B,gBAAgB,EAAE,MAAM,SAAS,CAAC;AAClE,IAAM,YAAN,cAAwB,cAAc;AAAA,EAClC,cAAc;AACV,UAAM,GAAG,SAAS;AAClB,SAAK,YAAY,EAAU;AAC3B,SAAK,QAAQ;AAAA,MACT,aAAa,eAAe;AAAA,IAChC;AACA,SAAK,eAAe,CAAC,OAAO;AACxB,aAAO,KAAK,WAAW,EAAE;AACzB,aAAO,KAAK,MAAM,OAAO,EAAE;AAAA,IAC/B;AAAA,EACJ;AAAA,EACA,SAAS;AACL,QAAI,EAAE,SAAS,OAAO,OAAO,UAAU,IAAI;AAC3C,QAAI,EAAE,SAAS,QAAQ,IAAI;AAC3B,QAAI,EAAE,MAAM,YAAY,IAAI;AAE5B,UAAM,eAAe,MAAM,iBACvB,wBAAwB,MAAM,YAAY,cAAc,OAAO;AACnE,WAAQ,EAAc,kBAAkB,EAAE,OAAO,MAAM,OAAO,KAAK,cAAc,WAAW;AAAA,MACpF;AAAA,MACA,GAAI,MAAM,mBAAmB,CAAC;AAAA,IAClC,GAAG,SAAS,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,MAAM,cAAc,GAAI,MAAM,gBAAgB,EAAE,mBAAmB,MAAM,YAAY,IAAI,CAAC,CAAE,GAAG,EAAE,MAAM,WAAW,CAAC,GAAG,kBAAkB,gBAAgB,MAAY,aAA0B,YAAY,MAAM,YAAY,eAAe,MAAM,eAAe,cAA4B,kBAAkB,MAAM,iBAAiB,GAAG,CAAC,cAAc,gBAAiB;AAAA,MAAc;AAAA,MAAO,EAAE,KAAK,MAAM,YAAY,WAAW,iDAAiD,OAAO,EAAE,WAAW,MAAM,UAAU,EAAE;AAAA,MACljB,MAAM,kBAAmB,EAAc,qBAAqB,EAAE,OAAO,KAAK,WAAW,CAAC,wBAAwB,GAAG,SAAS,kBAAkB,SAAS,MAAM,MAAM,GAAG,MAAY,eAAe,wBAAwB,CAAC;AAAA,MACxN,CAAC,YAAY,eACR,MAAM,iBAAiB,wBAAwB,OAAO,KAAK,MAAM,eAAgB;AAAA,QAAc;AAAA,QAAO,EAAE,WAAW,qBAAqB;AAAA,QACzI,EAAc,cAAc,EAAE,OAAO,KAAK,WAAW;AAAA,UAC7C;AAAA,UACA,gBAAgB;AAAA,QACpB,GAAG,SAAS,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,kBAAkB,SAAS,IAAI,CAAC,GAAG,EAAE,IAAI,MAAM,YAAY,CAAC,EAAE,CAAC;AAAA,MAAC,IAAK,MAAM;AAAA;AAAA,QAE/H;AAAA,UAAc;AAAA,UAAO,EAAE,WAAW,sBAAsB,OAAO,EAAE,YAAY,SAAS,EAAE;AAAA,UACpF,EAAc,KAAK,EAAE,WAAW,wBAAwB,GAAG,GAAQ;AAAA,QAAC;AAAA,UAAK;AAAA,MAC7E;AAAA,QAAc;AAAA,QAAO,EAAE,WAAW,yBAAyB,KAAK,MAAM,eAAe;AAAA,QACjF,MAAM;AAAA,QACN;AAAA,UAAc;AAAA,UAAO,EAAE,WAAW,yBAAyB,OAAO,EAAE,WAAW,MAAM,cAAc,EAAE;AAAA,UACjG,EAAc,mBAAmB,EAAE,YAAY,MAAM,kBAAkB,MAAM,kBAAkB,SAAS,MAAM,SAAS,gBAAgB,WAAW,cAAc,CAAC,MAAM,eAAe,eAAe,MAAM,eAAe,aAAa,MAAM,aAAa,gBAAgB,MAAM,gBAAgB,WAAW,MAAM,WAAW,aAAa,MAAM,aAAa,YAAY,MAAM,WAAW,CAAC;AAAA,QAAC;AAAA,MAAC;AAAA,MACpY,EAAc,OAAO,EAAE,WAAW,oBAAoB,GAAG,MAAM,SAAS;AAAA,IAAC,CAAE;AAAA,EACnF;AACJ;AACA,SAAS,eAAe,OAAO;AAC3B,SAAO,MAAM,iBAAiB,EAAc,GAAU,MAAM,GAAQ;AACxE;AACA,SAAS,wBAAwB,MAAM,cAAc,SAAS;AAC1D,QAAM,EAAE,OAAO,cAAc,KAAK,WAAW,IAAI;AACjD,QAAM,iBAAiB,MAAM,YAAY,EAAE;AAC3C,QAAM,mBAAmB,QAAQ,QAAQ,YAAY;AACrD,QAAM,oBAAoB,QAAQ,SAAS,YAAY;AACvD,QAAM,kBAAkB,QAAQ,QAAQ,cAAc;AACtD,QAAM,mBAAmB,QAAQ,SAAS,cAAc;AAExD,SAAO,EAAE,qBAAqB,mBAAmB,sBAAsB,qBACnE;AAAA;AAAA,IAEA,KAAK,QAAQ,MAAM,aAAa,QAAQ;AAAA,IAEnC,QAAQ,OAAO,IAAI,MAAM,KAAK,KAAK,QAAQ,IAAI,WAAW,QAAQ;AAAA,EAAE;AACjF;AAEA,SAAS,eAAe,KAAK;AACzB,SAAO,IAAI,WAAW,SAAS,aAAa,MAAM,IAAI;AAC1D;AACA,SAAS,eAAe,KAAK;AACzB,SAAO,eAAe,GAAG,IAAI,MAAM,IAAI;AAC3C;AACA,SAAS,sBAAsB,MAC/B,cAAc,iBAAiB,aAAa,YAAY,kBAAkB,OAAO;AAC7E,MAAI,YAAY,IAAI,oBAAoB,CAAC,aAAa;AAElD,QAAI,SAAS,KAAK,SAAS,KAAK,EAAE,WAAW,SAAS,aAClD,MAAM,SAAS,KAAK,QACpB,OAAO,SAAS,KAAK,MAAM;AAE/B,WAAO,WAAW,MAAM,KAAK;AAAA,EACjC,CAAC;AACD,YAAU,iBAAiB;AAC3B,YAAU,cAAc;AACxB,MAAI,iBAAiB,QAAQ,oBAAoB,MAAM;AACnD,cAAU,WAAW;AACrB,cAAU,iBAAiB;AAAA,EAC/B,WACS,OAAO,iBAAiB,UAAU;AACvC,cAAU,cAAc;AAAA,EAC5B,WACS,OAAO,oBAAoB,UAAU;AAC1C,cAAU,cAAc;AACxB,cAAU,iBAAiB;AAAA,EAC/B;AAEA,MAAI,YAAY,CAAC;AACjB,MAAI,oBAAoB,CAAC;AACzB,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK,GAAG;AACrC,QAAI,MAAM,KAAK,CAAC;AAChB,QAAI,SAAS,eAAe,GAAG;AAC/B,QAAI,cAAc,WAAW,MAAM;AACnC,QAAI,eAAe,MAAM;AACrB,gBAAU,KAAK;AAAA,QACX,OAAO;AAAA,QACP,MAAM;AAAA,UACF,OAAO,IAAI;AAAA,UACX,KAAK,IAAI,UAAU;AAAA,QACvB;AAAA,MACJ,CAAC;AAAA,IACL,OACK;AACD,wBAAkB,KAAK,GAAG;AAAA,IAC9B;AAAA,EACJ;AACA,MAAI,gBAAgB,UAAU,QAAQ,SAAS;AAC/C,MAAI,WAAW,UAAU,QAAQ;AACjC,MAAI,EAAE,qBAAqB,oBAAoB,gBAAgB,IAAI,WAAW,UAAU,MAAM,KAAK;AACnG,MAAI,WAAW,CAAC;AAChB,MAAI,iBAAiB,CAAC;AAEtB,WAAS,OAAO,mBAAmB;AAC/B,uBAAmB,IAAI,QAAQ,EAAE,KAAK;AAAA,MAClC;AAAA,MACA,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,WAAW;AAAA,IACf,CAAC;AACD,aAAS,MAAM,IAAI,UAAU,OAAO,IAAI,SAAS,OAAO,GAAG;AACvD,0BAAoB,GAAG,EAAE,KAAK;AAAA,QAC1B,KAAK,WAAW,KAAK,KAAK,MAAM,GAAG,KAAK;AAAA,QACxC,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,WAAW;AAAA,MACf,CAAC;AAAA,IACL;AAAA,EACJ;AAEA,WAAS,MAAM,GAAG,MAAM,MAAM,QAAQ,OAAO,GAAG;AAC5C,aAAS,KAAK,CAAC;AAAA,EACnB;AACA,WAAS,eAAe,eAAe;AACnC,QAAI,MAAM,KAAK,YAAY,KAAK;AAChC,QAAI,aAAa,YAAY;AAC7B,uBAAmB,WAAW,KAAK,EAAE,KAAK;AAAA,MACtC,KAAK,WAAW,KAAK,WAAW,OAAO,WAAW,KAAK,KAAK;AAAA,MAC5D,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,WAAW;AAAA,IACf,CAAC;AACD,aAAS,MAAM,WAAW,OAAO,MAAM,WAAW,KAAK,OAAO,GAAG;AAC7D,eAAS,GAAG,KAAK;AACjB,0BAAoB,GAAG,EAAE,KAAK;AAAA,QAC1B,KAAK,WAAW,KAAK,KAAK,MAAM,GAAG,KAAK;AAAA,QACxC,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,WAAW;AAAA,MACf,CAAC;AAAA,IACL;AAAA,EACJ;AAEA,WAAS,MAAM,GAAG,MAAM,MAAM,QAAQ,OAAO,GAAG;AAC5C,mBAAe,KAAK,gBAAgB,GAAG,CAAC;AAAA,EAC5C;AACA,SAAO,EAAE,qBAAqB,oBAAoB,UAAU,eAAe;AAC/E;AAEA,SAAS,WAAW,UAAU,MAAM,OAAO;AACvC,MAAI,iBAAiB,oBAAoB,UAAU,MAAM,MAAM;AAC/D,MAAI,sBAAsB,CAAC;AAC3B,MAAI,qBAAqB,CAAC;AAC1B,MAAI,kBAAkB,CAAC;AACvB,WAAS,MAAM,GAAG,MAAM,MAAM,QAAQ,OAAO,GAAG;AAC5C,QAAI,QAAQ,eAAe,GAAG;AAE9B,QAAI,mBAAmB,CAAC;AACxB,QAAI,gBAAgB;AACpB,QAAI,mBAAmB;AACvB,aAAS,QAAQ,OAAO;AACpB,UAAI,MAAM,KAAK,KAAK,KAAK;AACzB,uBAAiB,KAAK;AAAA,QAClB,KAAK,WAAW,KAAK,KAAK,MAAM,GAAG,KAAK;AAAA,QACxC,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,aAAa,KAAK;AAAA,QAClB,WAAW,KAAK,aAAa;AAAA,MACjC,CAAC;AACD,sBAAgB,KAAK,aAAa,KAAK;AAAA,IAC3C;AAEA,QAAI,kBAAkB,CAAC;AACvB,oBAAgB;AAChB,uBAAmB;AACnB,aAAS,QAAQ,OAAO;AACpB,UAAI,MAAM,KAAK,KAAK,KAAK;AACzB,UAAI,aAAa,KAAK,KAAK,MAAM,KAAK,KAAK,QAAQ;AACnD,UAAI,aAAa,KAAK,KAAK,UAAU;AACrC,0BAAoB,KAAK,aAAa;AACtC,sBAAgB,KAAK,aAAa,KAAK;AACvC,UAAI,YAAY;AACZ,4BAAoB,KAAK;AACzB,YAAI,YAAY;AACZ,0BAAgB,KAAK;AAAA,YACjB,KAAK,WAAW,KAAK,KAAK,KAAK,OAAO,KAAK,KAAK,KAAK,KAAK;AAAA,YAC1D,WAAW;AAAA,YACX,YAAY;AAAA,YACZ,aAAa,KAAK;AAAA,YAClB,WAAW;AAAA,UACf,CAAC;AAAA,QACL;AAAA,MACJ,WACS,YAAY;AACjB,wBAAgB,KAAK;AAAA,UACjB,KAAK,WAAW,KAAK,KAAK,KAAK,OAAO,KAAK,KAAK,KAAK,KAAK;AAAA,UAC1D,WAAW;AAAA,UACX,YAAY;AAAA,UACZ,aAAa,KAAK;AAAA,UAClB,WAAW;AAAA;AAAA,QACf,CAAC;AACD,2BAAmB;AAAA,MACvB;AAAA,IACJ;AACA,wBAAoB,KAAK,gBAAgB;AACzC,uBAAmB,KAAK,eAAe;AACvC,oBAAgB,KAAK,gBAAgB;AAAA,EACzC;AACA,SAAO,EAAE,qBAAqB,oBAAoB,gBAAgB;AACtE;AACA,SAAS,oBAAoB,OAAO,QAAQ;AACxC,MAAI,iBAAiB,CAAC;AACtB,WAAS,MAAM,GAAG,MAAM,QAAQ,OAAO,GAAG;AACtC,mBAAe,KAAK,CAAC,CAAC;AAAA,EAC1B;AACA,WAAS,QAAQ,OAAO;AACpB,aAAS,MAAM,KAAK,KAAK,OAAO,MAAM,KAAK,KAAK,KAAK,OAAO,GAAG;AAC3D,qBAAe,GAAG,EAAE,KAAK,IAAI;AAAA,IACjC;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,WAAW,KAAK,WAAW,SAAS,OAAO;AAChD,MAAI,IAAI,aAAa,aAAa,IAAI,YAAY,UAAU,GAAG;AAC3D,WAAO;AAAA,EACX;AACA,MAAI,aAAa,IAAI;AACrB,MAAI,YAAY,WAAW;AAC3B,MAAI,cAAc,gBAAgB,WAAW;AAAA,IACzC,OAAO,MAAM,SAAS,EAAE;AAAA,IACxB,KAAK,QAAQ,MAAM,UAAU,CAAC,EAAE,MAAM,CAAC;AAAA,EAC3C,CAAC;AACD,SAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,GAAG,GAAG,EAAE,UAAU,WAAW,SAAS,UAAU,GAAG,YAAY;AAAA,IAC9F,KAAK,WAAW;AAAA,IAChB,IAAI,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,WAAW,EAAE,GAAG,EAAE,kBAAkB,MAAM,CAAC;AAAA,IAC/E,UAAU,WAAW;AAAA,IACrB,OAAO;AAAA,EACX,GAAG,SAAS,IAAI,WAAW,YAAY,MAAM,QAAQ,MAAM,UAAU,MAAM,QAAQ,GAAG,OAAO,IAAI,SAAS,YAAY,IAAI,QAAQ,MAAM,UAAU,IAAI,QAAQ,EAAE,CAAC;AACzK;AACA,IAAM,sBAAN,cAAkC,aAAa;AAAA,EAC3C,cAAc;AACV,UAAM,GAAG,SAAS;AAElB,SAAK,iBAAiB;AAEtB,SAAK,cAAc,CAAC;AAAA,EACxB;AAAA,EACA,QAAQ,WAAW;AACf,UAAM,aAAa,MAAM,QAAQ,SAAS;AAC1C,UAAM,EAAE,eAAe,IAAI;AAC3B,UAAM,gBAAgB,CAAC,UAAU,CAAC,KAAK,YAAY,cAAc,KAAK,CAAC;AAEvE,aAAS,QAAQ,GAAG,QAAQ,eAAe,QAAQ,SAAS,GAAG;AAC3D,qBAAe,KAAK,IAAI,eAAe,KAAK,EAAE,OAAO,aAAa;AAAA,IACtE;AACA,WAAO;AAAA,EACX;AAAA,EACA,uBAAuB,WAAW,OAAO,eAAe;AACpD,UAAM,EAAE,gBAAgB,YAAY,IAAI;AACxC,UAAM,EAAE,eAAe,eAAe,gBAAgB,IAAI;AAE1D,QAAI,KAAK,kBAAkB,eAAe;AACtC,YAAM,kBAAkB,cAAc,aAAa;AACnD,UAAI,CAAC,YAAY,eAAe,GAAG;AAC/B,YAAI,KAAK,gBAAgB;AAErB,gBAAM,cAAc,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,aAAa,GAAG,EAAE,MAAM,eAAe,cAAc,MAAM,MAAM,IAAI,EAAE,CAAC;AAG5H,gBAAM,gBAAgB,cAAc,WAAW;AAC/C,sBAAY,aAAa,IAAI;AAC7B,yBAAe,aAAa,EAAE,eAAe,IAAI;AACjD,wBAAc,KAAK,WAAW;AAC9B,eAAK,WAAW,eAAe,OAAO,aAAa;AAAA,QACvD,OACK;AACD,sBAAY,eAAe,IAAI;AAC/B,wBAAc,KAAK,aAAa;AAAA,QACpC;AAAA,MACJ;AAAA,IACJ;AAEA,UAAM,uBAAuB,WAAW,OAAO,aAAa;AAAA,EAChE;AACJ;AAEA,IAAM,WAAN,cAAuB,cAAc;AAAA,EACjC,cAAc;AACV,UAAM,GAAG,SAAS;AAClB,SAAK,aAAa,IAAI,OAAO;AAC7B,SAAK,cAAc,IAAI,OAAO;AAC9B,SAAK,WAAW,IAAI,OAAO;AAC3B,SAAK,iBAAiB,IAAI,OAAO;AACjC,SAAK,YAAY,EAAU;AAC3B,SAAK,QAAQ;AAAA,MACT,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,MAClB,YAAY,CAAC;AAAA,IACjB;AACA,SAAK,eAAe,CAAC,aAAa;AAC9B,UAAI,UAAU;AACV,aAAK,aAAa,IAAI;AAAA,MAC1B;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,SAAS;AACL,QAAI,EAAE,OAAO,OAAO,QAAQ,IAAI;AAChC,QAAI,EAAE,QAAQ,IAAI;AAClB,QAAI,SAAS,MAAM,MAAM;AACzB,QAAI,qBAAqB,oBAAoB,MAAM,kBAAkB,MAAM;AAC3E,QAAI,mBAAmB,oBAAoB,MAAM,aAAa,MAAM;AACpE,QAAI,qBAAqB,oBAAoB,KAAK,iBAAiB,GAAG,MAAM;AAC5E,QAAI,kBAAkB,oBAAoB,KAAK,cAAc,GAAG,MAAM;AACtE,QAAI,EAAE,qBAAqB,oBAAoB,UAAU,eAAe,IAAI,sBAAsB,cAAc,MAAM,aAAa,QAAQ,UAAU,GAAG,MAAM,cAAc,MAAM,iBAAiB,QAAQ,kBAAkB,MAAM,YAAY,MAAM,kBAAkB,MAAM,KAAK;AAClR,QAAI;AAAA;AAAA,MACF,MAAM,aAAa,MAAM,UAAU,qBAChC,MAAM,eAAe,MAAM,YAAY,qBACxC,CAAC;AAAA;AACL,WAAQ;AAAA,MAAc;AAAA,MAAM,EAAE,KAAK,KAAK,WAAW,MAAM,MAAM;AAAA,MAC3D,MAAM,eAAe,MAAM,YAAY;AAAA,MACvC,MAAM,MAAM,IAAI,CAAC,MAAM,QAAQ;AAC3B,YAAI,gBAAgB,KAAK,aAAa,KAAK,MAAM,WAAW,oBAAoB,GAAG,IAAI,mBAAmB,GAAG,GAAG,MAAM,YAAY,iBAAiB;AACnJ,YAAI,gBAAgB,KAAK,aAAa,KAAK,sBAAsB,gBAAgB,GAAG,GAAG,kBAAkB,GAAG,MAAM,YAAY,CAAC,GAAG,QAAQ,MAAM,SAAS,GAAG,QAAQ,MAAM,WAAW,GAAG,KAAK;AAC7L,eAAQ,EAAc,WAAW,EAAE,KAAK,KAAK,KAAK,OAAO,KAAK,WAAW,UAAU,KAAK,GAAG,GAAG,YAAY,KAAK,YAAY,UAAU,KAAK,GAAG,GAA4E,aAAa,MAAM,aAAa,MAAM,KAAK,MAAM,eAAe,MAAM,gBAAgB,gBAAgB,MAAM,mBAAmB,QAAQ,GAAG,aAAa,MAAM,iBAA4E,YAAY,MAAM,YAAY,gBAAgB,MAAM,gBAAgB,WAAW,MAAM,WAAW,aAAa,MAAM,aAAa,kBAAkB,KAAK,kBAAkB,gBAAgB,KAAK,gBAAgB,iBAAiB,KAAK,iBAAiB,eAAe,KAAK,eAAe,SAAS,SAAS,GAAG,GAAG,eAAe,eAAe,GAAG,GAAG,kBAAkB,oBAAoB,GAAG,GAAG,gBAAgB,KAAK,SAAS,UAAU,KAAK,GAAG,GAAG;AAAA;AAAA,UACj3B;AAAA,YAAc;AAAA,YAAU;AAAA,YACpB,EAAc,GAAU,MAAM,aAAa;AAAA,YAC3C,EAAc,GAAU,MAAM,aAAa;AAAA,UAAC;AAAA,WAAI;AAAA;AAAA,UACpD;AAAA,YAAc;AAAA,YAAU;AAAA,YACpB,KAAK,eAAe,mBAAmB,GAAG,GAAG,WAAW;AAAA,YACxD,KAAK,eAAe,mBAAmB,GAAG,GAAG,cAAc;AAAA,YAC3D,KAAK,eAAe,iBAAiB,GAAG,GAAG,UAAU;AAAA,UAAC;AAAA,WAAI,WAAW,MAAM,cAAc,CAAC;AAAA,MACtG,CAAC;AAAA,IAAC;AAAA,EACV;AAAA,EACA,oBAAoB;AAChB,SAAK,aAAa,IAAI;AACtB,SAAK,QAAQ,iBAAiB,KAAK,YAAY;AAAA,EACnD;AAAA,EACA,mBAAmB,WAAW,WAAW;AACrC,QAAI,eAAe,KAAK;AACxB,SAAK,aAAa,CAAC,aAAa,WAAW,YAAY,CAAC;AAAA,EAC5D;AAAA,EACA,uBAAuB;AACnB,SAAK,QAAQ,oBAAoB,KAAK,YAAY;AAAA,EACtD;AAAA,EACA,mBAAmB;AACf,QAAI,EAAE,MAAM,IAAI;AAChB,QAAI,MAAM,aAAa,MAAM,UAAU,KAAK,QAAQ;AAChD,aAAO,MAAM,UAAU;AAAA,IAC3B;AACA,QAAI,MAAM,eAAe,MAAM,YAAY,KAAK,QAAQ;AACpD,aAAO,MAAM,YAAY;AAAA,IAC7B;AACA,WAAO,MAAM;AAAA,EACjB;AAAA,EACA,gBAAgB;AACZ,QAAI,EAAE,MAAM,IAAI;AAChB,QAAI,MAAM,eAAe,MAAM,YAAY,KAAK,QAAQ;AACpD,aAAO,MAAM,YAAY;AAAA,IAC7B;AACA,WAAO,CAAC;AAAA,EACZ;AAAA,EACA,aAAa,KAAK,eAAe,YAAY,mBAAmB,YAAY,YAAY,iBAAiB;AACrG,QAAI,EAAE,QAAQ,IAAI;AAClB,QAAI,EAAE,eAAe,IAAI,KAAK;AAC9B,QAAI,EAAE,eAAe,IAAI,KAAK;AAC9B,QAAI,yBAAyB,KAAK,MAAM,MAAM,WAAW;AACzD,QAAI,WAAW,cAAc,cAAc;AAC3C,QAAI,QAAQ,CAAC;AACb,QAAI,gBAAgB;AAChB,eAAS,aAAa,eAAe;AACjC,YAAI,EAAE,IAAI,IAAI;AACd,YAAI,EAAE,WAAW,IAAI,IAAI,WAAW;AACpC,YAAI,YAAY,UAAU,aAAa,CAAC,kBAAkB,UAAU;AACpE,YAAI,aAAa,UAAU;AAC3B,YAAI,OAAO;AACX,YAAI,QAAQ;AACZ,YAAI,YAAY;AACZ,cAAI,QAAQ,OAAO;AACf,oBAAQ;AACR,mBAAO,eAAe,MAAM,IAAI,OAAO,IAAI,eAAe,MAAM,IAAI,QAAQ;AAAA,UAChF,OACK;AACD,mBAAO;AACP,oBAAQ,eAAe,OAAO,IAAI,QAAQ,IAAI,eAAe,OAAO,IAAI,OAAO;AAAA,UACnF;AAAA,QACJ;AAKA,cAAM,KAAK,EAAc,OAAO,EAAE,WAAW,8BAA8B,aAAa,kCAAkC,KAAK,KAAK,eAAe,GAAG,GAAG,KAAK,WAAW,OAAO,KAAK,eAAe,UAAU,eAAe,GAAG,CAAC,GAAG,OAAO;AAAA,UACnO,YAAY,YAAY,KAAK;AAAA,UAC7B,WAAW,aAAa,KAAK,UAAU;AAAA,UACvC,KAAK,aAAa,UAAU,cAAc;AAAA,UAC1C;AAAA,UACA;AAAA,QACJ,EAAE,GAAG,mBAAmB,GAAG,IAAK,EAAc,oBAAoB,OAAO,OAAO,EAAE,KAAU,YAAwB,YAAY,eAAe,gBAAgB,uBAA+C,GAAG,WAAW,KAAK,UAAU,CAAC,CAAC,IAAM,EAAc,iBAAiB,OAAO,OAAO,EAAE,KAAU,YAAwB,YAAwB,iBAAkC,YAAY,eAAe,gBAAgB,uBAA+C,GAAG,WAAW,KAAK,UAAU,CAAC,CAAC,CAAE,CAAC;AAAA,MAClgB;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA,EACA,eAAe,MAAM,UAAU;AAC3B,QAAI,EAAE,MAAM,IAAI,KAAK;AACrB,QAAI,EAAE,WAAW,IAAI,KAAK;AAC1B,QAAI,EAAE,eAAe,IAAI,KAAK;AAC9B,QAAI,QAAQ,CAAC;AACb,QAAI,gBAAgB;AAChB,eAAS,OAAO,MAAM;AAClB,YAAI,eAAe,QAAQ;AAAA,UACvB,OAAO;AAAA,UACP,MAAM,eAAe,MAAM,IAAI,OAAO,IAAI,eAAe,MAAM,IAAI,QAAQ;AAAA,QAC/E,IAAI;AAAA,UACA,MAAM;AAAA,UACN,OAAO,eAAe,OAAO,IAAI,QAAQ,IAAI,eAAe,OAAO,IAAI,OAAO;AAAA,QAClF;AACA,cAAM,KAAK,EAAc,OAAO,EAAE,KAAK,mBAAmB,IAAI,UAAU,GAAG,WAAW,yBAAyB,OAAO,aAAa,GAAG,aAAa,aAC/I,EAAc,SAAS,OAAO,OAAO,EAAE,IAAS,GAAG,WAAW,KAAK,UAAU,CAAC,CAAC,IAC/E,WAAW,QAAQ,CAAC,CAAC;AAAA,MAC7B;AAAA,IACJ;AACA,WAAO,EAAc,GAAU,CAAC,GAAG,GAAG,KAAK;AAAA,EAC/C;AAAA,EACA,aAAa,wBAAwB;AACjC,QAAI,EAAE,OAAO,OAAO,YAAY,IAAI;AACpC,QAAI,CAAC,MAAM,YACP,MAAM,gBAAgB,MACxB;AACE,UAAI,wBAAwB;AACxB,YAAI,WAAW,MAAM,MAAM,IAAI,CAAC,SAAS,YAAY,WAAW,KAAK,GAAG,CAAC;AACzE,YAAI,SAAS,QAAQ;AACjB,cAAI,WAAW,KAAK,UAAU;AAC9B,cAAI,mBAAmB,IAAI;AAAA,YAAc;AAAA,YAAU;AAAA,YAAU;AAAA;AAAA,YAC7D;AAAA,UAAK;AACL,cAAI,CAAC,MAAM,kBAAkB,CAAC,MAAM,eAAe,UAAU,gBAAgB,GAAG;AAC5E,iBAAK,SAAS;AAAA,cACV,gBAAgB,IAAI;AAAA,gBAAc;AAAA,gBAAU;AAAA,gBAAU;AAAA;AAAA,gBACtD;AAAA,cAAK;AAAA,YACT,CAAC;AAAA,UACL;AAAA,QACJ;AAAA,MACJ;AACA,YAAM,gBAAgB,KAAK,MAAM;AACjC,YAAM,gBAAgB,KAAK,gBAAgB;AAC3C,YAAM,uBAAuB,MAAM,iBAAiB,QAAQ,MAAM,oBAAoB;AACtF,WAAK,aAAa;AAAA;AAAA;AAAA;AAAA,QAId,YAAY,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,aAAa,GAAG,aAAa;AAAA,QACzE,kBAAkB,uBAAuB,KAAK,wBAAwB,IAAI;AAAA,MAC9E,CAAC;AAAA,IACL;AAAA,EACJ;AAAA,EACA,kBAAkB;AACd,QAAI,WAAW,KAAK,eAAe;AACnC,QAAI,aAAa,CAAC;AAElB,aAAS,UAAU,UAAU;AACzB,UAAI,SAAS,KAAK,MAAM,SAAS,MAAM,EAAE,sBAAsB,EAAE,MAAM;AACvE,iBAAW,MAAM,IAAI,KAAK,IAAI,WAAW,MAAM,KAAK,GAAG,MAAM;AAAA,IACjE;AACA,WAAO;AAAA,EACX;AAAA,EACA,0BAA0B;AACtB,QAAI,WAAW,KAAK,MAAM,MAAM,CAAC,EAAE;AACnC,QAAI,SAAS,KAAK,WAAW,WAAW,QAAQ;AAChD,QAAI,gBAAgB,KAAK,SAAS,WAAW,QAAQ;AACrD,WAAO,OAAO,sBAAsB,EAAE,SAAS,cAAc,sBAAsB,EAAE;AAAA,EACzF;AAAA,EACA,aAAa;AACT,QAAI,QAAQ,KAAK,WAAW;AAC5B,WAAO,KAAK,MAAM,MAAM,IAAI,CAAC,SAAS,MAAM,KAAK,GAAG,CAAC;AAAA,EACzD;AACJ;AACA,SAAS,iBAAiB;AAAA,EACtB,YAAY;AAChB,CAAC;AACD,SAAS,sBAAsB,YAAY,eAAe;AACtD,MAAI,CAAC,WAAW,QAAQ;AACpB,WAAO,CAAC;AAAA,EACZ;AACA,MAAI,mBAAmB,qBAAqB,aAAa;AACzD,SAAO,WAAW,IAAI,CAAC,SAAS;AAAA,IAC5B;AAAA,IACA,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa,iBAAiB,IAAI,WAAW,SAAS,UAAU;AAAA,IAChE,WAAW;AAAA,EACf,EAAE;AACN;AACA,SAAS,qBAAqB,eAAe;AACzC,MAAI,mBAAmB,CAAC;AACxB,WAAS,cAAc,eAAe;AAClC,aAAS,aAAa,YAAY;AAC9B,uBAAiB,UAAU,IAAI,WAAW,SAAS,UAAU,IAAI,UAAU;AAAA,IAC/E;AAAA,EACJ;AACA,SAAO;AACX;AAEA,IAAM,YAAN,cAAwB,cAAc;AAAA,EAClC,cAAc;AACV,UAAM,GAAG,SAAS;AAClB,SAAK,wBAAwB,QAAQ,cAAc;AACnD,SAAK,mBAAmB,QAAQ,cAAc;AAC9C,SAAK,mBAAmB,QAAQ,cAAc;AAC9C,SAAK,yBAAyB,QAAQ,cAAc;AACpD,SAAK,iBAAiB,QAAQ,qBAAqB;AACnD,SAAK,mBAAmB,QAAQ,qBAAqB;AACrD,SAAK,UAAU,IAAI,OAAO;AAAA,EAC9B;AAAA,EACA,SAAS;AACL,QAAI,EAAE,OAAO,QAAQ,IAAI;AACzB,QAAI,SAAS,MAAM,MAAM;AACzB,QAAI,wBAAwB,KAAK,sBAAsB,MAAM,kBAAkB,MAAM;AACrF,QAAI,mBAAmB,KAAK,iBAAiB,MAAM,aAAa,MAAM;AACtE,QAAI,mBAAmB,KAAK,iBAAiB,MAAM,aAAa,MAAM;AACtE,QAAI,yBAAyB,KAAK,uBAAuB,MAAM,mBAAmB,MAAM;AACxF,QAAI,iBAAiB,KAAK,eAAe,MAAM,WAAW,MAAM;AAChE,QAAI,mBAAmB,KAAK,iBAAiB,MAAM,aAAa,MAAM;AAGtE,QAAI,gBAAiB,UAAU,KAAK,MAAM,cACtC,MAAM,cAAc,QAAQ,QAAQ,cAAc,IAClD;AACJ,WAAQ,EAAc,UAAU,EAAE,MAAM,MAAM,GAAG,CAAC,SAAS,eAAgB,EAAc,GAAU,MAAM,MAAM,MAAM,IAAI,CAAC,OAAO,QAAS,EAAc,UAAU;AAAA,MAAE,KAAK,KAAK,QAAQ,UAAU,GAAG;AAAA,MAAG,KAAK,MAAM,SACvM,MAAM,CAAC,EAAE,KAAK,YAAY,IAC1B;AAAA,MACJ,gBAAgB,SAAS;AAAA,MAAG,iBAAiB,MAAM;AAAA,MAAiB;AAAA,MAAwB,aAAa,MAAM;AAAA,MAAa;AAAA,MAAc,aAAa,MAAM;AAAA,MAAgB,kBAAkB,sBAAsB,GAAG;AAAA,MAAG,gBAAgB,MAAM;AAAA,MAAgB,aAAa,iBAAiB,GAAG,EAAE,OAAO,WAAW;AAAA,MAAc,aAAa,iBAAiB,GAAG;AAAA,MAAG,mBAAmB,uBAAuB,GAAG;AAAA,MAAG,WAAW,eAAe,GAAG;AAAA,MAAG,aAAa,iBAAiB,GAAG;AAAA,MAAG,cAAc,MAAM;AAAA,MAAc,iBAAiB,MAAM;AAAA,MAAiB,aAAa,MAAM;AAAA,MAAa,cAAc,MAAM;AAAA,MAAc;AAAA,MAA8B,UAAU,MAAM;AAAA,IAAS,CAAC,CAAE,CAAC,CAAE;AAAA,EAC/qB;AAAA,EACA,oBAAoB;AAChB,SAAK,6BAA6B;AAAA,EACtC;AAAA,EACA,qBAAqB;AAEjB,SAAK,6BAA6B;AAAA,EACtC;AAAA,EACA,+BAA+B;AAC3B,QAAI,CAAC,KAAK,QAAQ;AAGd,YAAM,cAAc,KAAK,QAAQ,WAAW,CAAC,EAAE,WAAW,EAAE,CAAC;AAC7D,YAAM,SAAS,cAAc,YAAY,QAAQ,kBAAkB,IAAI;AACvE,UAAI,QAAQ;AACR,aAAK,SAAS;AACd,aAAK,QAAQ,6BAA6B,MAAM;AAAA,UAC5C,IAAI;AAAA,UACJ,mBAAmB,KAAK,MAAM;AAAA,QAClC,CAAC;AAAA,MACL;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,uBAAuB;AACnB,QAAI,KAAK,QAAQ;AACb,WAAK,QAAQ,+BAA+B,IAAI;AAChD,WAAK,SAAS;AAAA,IAClB;AAAA,EACJ;AAAA;AAAA;AAAA,EAGA,cAAc;AACV,SAAK,eAAe,IAAI;AAAA,MAAc,KAAK;AAAA,MAAQ,KAAK,QAAQ,QAAQ,EAAE,IAAI,CAAC,WAAW,OAAO,WAAW,EAAE,CAAC,CAAC;AAAA;AAAA,MAChH;AAAA,MAAO;AAAA,IAAI;AACX,SAAK,eAAe,IAAI;AAAA,MAAc,KAAK;AAAA,MAAQ,KAAK,QAAQ,WAAW,CAAC,EAAE,WAAW;AAAA;AAAA,MACzF;AAAA;AAAA,MACA;AAAA,IAAK;AAAA,EACT;AAAA,EACA,SAAS,cAAc,aAAa;AAChC,QAAI,EAAE,cAAc,aAAa,IAAI;AACrC,QAAI,MAAM,aAAa,YAAY,YAAY;AAC/C,QAAI,MAAM,aAAa,WAAW,WAAW;AAC7C,QAAI,OAAO,QAAQ,OAAO,MAAM;AAC5B,UAAI,OAAO,KAAK,MAAM,MAAM,GAAG,EAAE,GAAG;AACpC,aAAO;AAAA,QACH,aAAa,KAAK,MAAM;AAAA,QACxB,UAAU,OAAO,OAAO,EAAE,OAAO,KAAK,aAAa,KAAK,GAAG,GAAG,QAAQ,KAAK,GAAG,KAAK,aAAa;AAAA,QAChG,OAAO,KAAK,UAAU,KAAK,GAAG;AAAA,QAC9B,MAAM;AAAA,UACF,MAAM,aAAa,MAAM,GAAG;AAAA,UAC5B,OAAO,aAAa,OAAO,GAAG;AAAA,UAC9B,KAAK,aAAa,KAAK,GAAG;AAAA,UAC1B,QAAQ,aAAa,QAAQ,GAAG;AAAA,QACpC;AAAA,QACA,OAAO;AAAA,MACX;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA,EACA,UAAU,KAAK,KAAK;AAChB,WAAO,KAAK,QAAQ,WAAW,GAAG,EAAE,WAAW,EAAE,GAAG;AAAA,EACxD;AAAA,EACA,aAAa,KAAK,KAAK;AACnB,QAAI,QAAQ,KAAK,MAAM,MAAM,GAAG,EAAE,GAAG,EAAE;AACvC,QAAI,MAAM,QAAQ,OAAO,CAAC;AAC1B,WAAO,EAAE,OAAO,IAAI;AAAA,EACxB;AACJ;AACA,SAAS,YAAY,KAAK;AACtB,SAAO,IAAI,WAAW,IAAI;AAC9B;AAEA,IAAM,QAAN,cAAoB,cAAc;AAAA,EAC9B,cAAc;AACV,UAAM,GAAG,SAAS;AAClB,SAAK,QAAQ,EAAU;AACvB,SAAK,mBAAmB;AAAA,EAC5B;AAAA,EACA,SAAS;AACL,QAAI,EAAE,MAAM,IAAI;AAChB,QAAI,EAAE,iBAAiB,cAAc,WAAW,IAAI;AACpD,QAAI,mBAAmB,iBAAiB,QAAQ,oBAAoB;AAGpE,QAAI,oBAAoB,CAAC,YAAY;AACjC,yBAAmB;AACnB,wBAAkB;AAClB,qBAAe;AAAA,IACnB;AACA,QAAI,aAAa;AAAA,MACb;AAAA,MACA,mBAAmB,6BAA6B;AAAA,MAChD,aAAa,KAAK;AAAA;AAAA,IACtB;AACA,WAAQ;AAAA,MAAc;AAAA,MAAO,EAAE,KAAK,KAAK,OAAO,WAAW,WAAW,KAAK,GAAG,GAAG,OAAO;AAAA;AAAA;AAAA,QAGhF,OAAO,MAAM;AAAA,QACb,UAAU,MAAM;AAAA,MACpB,EAAE;AAAA,MACF;AAAA,QAAc;AAAA,QAAS,EAAE,MAAM,gBAAgB,WAAW,4BAA4B,OAAO;AAAA,UACrF,OAAO,MAAM;AAAA,UACb,UAAU,MAAM;AAAA,UAChB,QAAQ,aAAa,MAAM,eAAe;AAAA,QAC9C,EAAE;AAAA,QACF,MAAM;AAAA,QACN;AAAA,UAAc;AAAA,UAAS,EAAE,MAAM,eAAe;AAAA,UAC1C,EAAc,WAAW,EAAE,aAAa,MAAM,aAAa,OAAO,MAAM,OAAO,gBAAgB,MAAM,gBAAgB,iBAAiB,MAAM,iBAAiB,aAAa,MAAM,aAAa,cAAc,MAAM,cAAc,kBAAkB,MAAM,kBAAkB,aAAa,MAAM,aAAa,aAAa,MAAM,aAAa,mBAAmB,MAAM,mBAAmB,gBAAgB,MAAM,gBAAgB,WAAW,MAAM,WAAW,aAAa,MAAM,aAAa,cAA4B,iBAAkC,UAAU,MAAM,UAAU,mBAAmB,MAAM,kBAAkB,CAAC;AAAA,QAAC;AAAA,MAAC;AAAA,IAAC;AAAA,EAC9mB;AAAA,EACA,oBAAoB;AAChB,SAAK,mBAAmB;AAAA,EAC5B;AAAA,EACA,mBAAmB,WAAW;AAC1B,QAAI,UAAU,gBAAgB,KAAK,MAAM,aAAa;AAClD,WAAK,mBAAmB;AAAA,IAC5B,OACK;AACD,WAAK,iBAAiB;AAAA,IAC1B;AAAA,EACJ;AAAA,EACA,qBAAqB;AACjB,SAAK,mBAAmB;AACxB,SAAK,iBAAiB;AAAA,EAC1B;AAAA,EACA,mBAAmB;AACf,QAAI,KAAK,oBACL,KAAK,MAAM,aACb;AACE,YAAM,YAAY,mBAAmB,KAAK,MAAM,SAAS,KAAK,MAAM,WAAW;AAC/E,UAAI,WAAW;AACX,cAAM,WAAW,UAAU,QAAQ,kBAAkB;AACrD,cAAM,WAAW,SAAS,QAAQ,cAAc;AAChD,cAAM,YAAY,UAAU,sBAAsB,EAAE,MAChD,SAAS,sBAAsB,EAAE;AACrC,iBAAS,YAAY,YAAa,YAAY,IAAK;AAAA,MACvD;AACA,WAAK,mBAAmB;AAAA,IAC5B;AAAA,EACJ;AACJ;AACA,SAAS,mBAAmB,aAAa,aAAa;AAClD,MAAI;AACJ,MAAI,YAAY,iBAAiB,MAAM,YAAY,GAAG;AAClD,SAAK,YAAY,cAAc,eAAe,kBAAkB,YAAY,WAAW,CAAC,OAAO;AAAA,EAEnG;AACA,MAAI,CAAC,IAAI;AACL,SAAK,YAAY,cAAc,eAAe,gBAAgB,YAAY,WAAW,CAAC,IAAI;AAAA,EAE9F;AACA,SAAO;AACX;AAEA,IAAM,iBAAN,cAA6B,OAAO;AAAA,EAChC,cAAc;AACV,UAAM,GAAG,SAAS;AAClB,SAAK,qBAAqB;AAAA,EAC9B;AAAA,EACA,WAAW,WAAW,eAAe;AACjC,WAAO,cAAc,WAAW,SAAS;AAAA,EAC7C;AACJ;AAEA,IAAM,WAAN,cAAuB,cAAc;AAAA,EACjC,cAAc;AACV,UAAM,GAAG,SAAS;AAClB,SAAK,SAAS,IAAI,eAAe;AACjC,SAAK,WAAW,EAAU;AAAA,EAC9B;AAAA,EACA,SAAS;AACL,QAAI,EAAE,OAAO,QAAQ,IAAI;AACzB,WAAQ,EAAc,OAAO,OAAO,OAAO,EAAE,KAAK,KAAK,SAAS,GAAG,KAAK,OAAO,WAAW,OAAO,MAAM,aAAa,MAAM,kBAAkB,SAAS,MAAM,aAAa,GAAG,EAAE,aAAa,MAAM,aAAa,OAAO,MAAM,cAAc,OAAO,cAAc,MAAM,cAAc,eAAe,MAAM,eAAe,gBAAgB,MAAM,gBAAgB,cAAc,MAAM,cAAc,iBAAiB,MAAM,iBAAiB,iBAAiB,MAAM,iBAAiB,YAAY,MAAM,YAAY,kBAAkB,MAAM,kBAAkB,aAAa,MAAM,aAAa,cAAc,MAAM,cAAc,UAAU,MAAM,SAAS,CAAC,CAAC;AAAA,EACtnB;AACJ;AAEA,IAAM,eAAN,cAA2B,UAAU;AAAA,EACjC,cAAc;AACV,UAAM,GAAG,SAAS;AAClB,SAAK,qBAAqB,QAAQ,kBAAkB;AACpD,SAAK,YAAY,EAAU;AAC3B,SAAK,WAAW,EAAU;AAAA,EAE9B;AAAA,EACA,SAAS;AACL,QAAI,EAAE,SAAS,qBAAqB,IAAI,KAAK;AAC7C,QAAI,EAAE,MAAM,IAAI;AAChB,QAAI,gBAAgB,KAAK,mBAAmB,MAAM,aAAa,oBAAoB;AACnF,QAAI,gBAAgB,QAAQ,cAAe,EAAc,WAAW,EAAE,KAAK,KAAK,WAAW,aAAa,MAAM,aAAa,OAAO,cAAc,aAAa,sBAAsB,cAAc,WAAW,EAAE,CAAC;AAC/M,QAAI,cAAc,CAAC,eAAgB,EAAc,UAAU,EAAE,KAAK,KAAK,UAAU,aAAa,MAAM,aAAa,eAA8B,eAAe,MAAM,eAAe,eAAe,MAAM,eAAe,YAAY,MAAM,YAAY,cAAc,MAAM,cAAc,gBAAgB,MAAM,gBAAgB,WAAW,MAAM,WAAW,aAAa,MAAM,aAAa,kBAAkB,QAAQ,kBAAkB,cAAc,WAAW,mBAAmB,eAAe,WAAW,eAAe,cAAc,QAAQ,cAAc,iBAAiB,QAAQ,iBAAiB,iBAAiB,QAAQ,aAAa,YAAY,CAAC,MAAM,cAAc,kBAAkB,KAAK,aAAa,aAAa,WAAW,aAAa,cAAc,WAAW,cAAc,UAAU,MAAM,SAAS,CAAC;AACzxB,WAAO,QAAQ,cACT,KAAK,oBAAoB,eAAe,aAAa,cAAc,QAAQ,QAAQ,WAAW,IAC9F,KAAK,mBAAmB,eAAe,WAAW;AAAA,EAC5D;AACJ;AACA,SAAS,mBAAmB,aAAa,sBAAsB;AAC3D,MAAI,YAAY,IAAI,eAAe,YAAY,aAAa,oBAAoB;AAChF,SAAO,IAAI,cAAc,WAAW,kBAAkB,KAAK,YAAY,gBAAgB,CAAC;AAC5F;AAEA,IAAM,4BAAN,cAAwC,qBAAqB;AAAA;AAAA,EAEzD,iBAAiB,cAAc,kBAAkB,eAAe;AAC5D,QAAI,cAAc,MAAM,iBAAiB,cAAc,kBAAkB,aAAa;AACtF,QAAI,EAAE,MAAM,IAAI;AAChB,WAAO,yBAAyB;AAAA,MAC5B,cAAc;AAAA,MACd,YAAY,iBAAiB,KAAK,gBAAgB;AAAA,MAClD,gBAAgB,MAAM;AAAA,MACtB,SAAS,MAAM;AAAA,IACnB,CAAC;AAAA,EACL;AACJ;AACA,SAAS,yBAAyB,OAAO;AACrC,MAAI,EAAE,SAAS,aAAa,IAAI;AAChC,MAAI,EAAE,OAAO,IAAI,IAAI;AACrB,MAAI;AAEJ,MAAI,MAAM,YAAY;AAClB,YAAQ,QAAQ,YAAY,KAAK;AAEjC,gBAAY,QAAQ,YAAY,GAAG;AACnC,QAAI,UAAU,QAAQ,MAAM,IAAI,QAAQ,GAAG;AACvC,YAAM,SAAS,WAAW,CAAC;AAAA,IAC/B;AAAA,EACJ;AAEA,MAAI,MAAM,gBAAgB;AAGtB,QAAI,uBAAuB,QAAQ,YAAY,QAAQ,aAAa,QAAQ,aAAa,KAAK,EAAE,CAAC,CAAC;AAClG,QAAI,SAAS,KAAK;AAAA;AAAA,MAClB,UAAU,sBAAsB,GAAG;AAAA,IAAC;AACpC,UAAM,SAAS,KAAK,IAAI,MAAM;AAAA,EAClC;AACA,SAAO,EAAE,OAAO,IAAI;AACxB;AAEA,IAAI,WAAW;AACf,aAAa,QAAQ;;;ACj9BrB,IAAI,QAAQ,aAAa;AAAA,EACrB,MAAM;AAAA,EACN,aAAa;AAAA,EACb,OAAO;AAAA,IACH,SAAS;AAAA,MACL,WAAW;AAAA,MACX,2BAA2B;AAAA,IAC/B;AAAA,IACA,YAAY;AAAA,MACR,MAAM;AAAA,MACN,UAAU,EAAE,MAAM,EAAE;AAAA,IACxB;AAAA,IACA,aAAa;AAAA,MACT,MAAM;AAAA,MACN,UAAU,EAAE,OAAO,EAAE;AAAA,IACzB;AAAA,IACA,cAAc;AAAA,MACV,MAAM;AAAA,MACN,UAAU,EAAE,QAAQ,EAAE;AAAA,MACtB,gBAAgB;AAAA,IACpB;AAAA,IACA,aAAa;AAAA,MACT,MAAM;AAAA,MACN,UAAU,EAAE,OAAO,EAAE;AAAA,IACzB;AAAA,EACJ;AACJ,CAAC;", "names": []}