{"name": "mpma-ojt-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@fortawesome/fontawesome-free": "^6.7.2", "@fullcalendar/core": "^6.1.15", "@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/interaction": "^6.1.15", "@fullcalendar/react": "^6.1.15", "@hookform/resolvers": "^3.9.0", "axios": "^1.7.7", "bootstrap": "^5.3.3", "bootstrap-icons": "^1.11.3", "bson": "^6.10.0", "fuse.js": "^7.0.0", "lucide-react": "^0.516.0", "mdb-react-ui-kit": "^9.0.0", "moment": "^2.30.1", "react": "^18.3.1", "react-bootstrap": "^2.10.5", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "react-router-dom": "^6.26.2", "react-select": "^5.8.1", "react-window": "^1.8.11", "sweetalert2": "^11.14.1", "xlsx": "^0.18.5", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.9.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/react-window": "^1.8.8", "@vitejs/plugin-react": "^4.3.1", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.1"}}