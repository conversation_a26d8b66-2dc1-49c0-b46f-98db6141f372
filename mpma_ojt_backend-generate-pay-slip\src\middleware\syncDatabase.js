import { sync } from "../DB/sequlize.js";
import { ErrorWithStatus } from "../ErrorWithStatus.js";
import { Trainee } from "../models/trainee.js";

export const syncDatabase = async (req, res, next) => {
  try {
    await sync();
    console.log("Database Connection have been Established Successfully");
    next();
  } catch (error) {
    //console.log(error);
    next(ErrorWithStatus(503, "failed to sync with database"));
  }
};

/**
 * sync the databse repetedly for every 30 seconds and console logs if eny error has been occuured
 */
