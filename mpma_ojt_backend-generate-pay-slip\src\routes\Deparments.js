import express from "express";
import {
  getDepartmentSummary,
  getinterviewDateSummary,
  getInterviews,
  index,
  show,
  store,
} from "../Controllers/DepartmentController.js";
import { Auth } from "../Core/AuthWrapper.js";
import { DEPARTMENT_MODIFY } from "../Core/Permisions.js";
const router = express.Router();

router.get("/", await <PERSON>th([DEPARTMENT_MODIFY]), index);
router.post("/", await <PERSON>th([DEPARTMENT_MODIFY]), store);
router.get("/summary", await <PERSON>th([DEPARTMENT_MODIFY]), getDepartmentSummary);
router.get("/:id", show);
router.get("/:id/interview/", getInterviews);
router.get(
  "/:id/interview/summary",
  getinterviewDateSummary
);
export default router;
