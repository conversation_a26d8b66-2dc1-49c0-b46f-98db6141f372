import { DataTypes } from "sequelize";
import { sequalize } from "../DB/sequlize.js";
import { date } from "zod";
import { Trainee } from "./trainee.js";

export const Attendence = sequalize.define(
  "Attendence",
  {
    id: { type: DataTypes.INTEGER, primaryKey: true, autoIncrement: true },
    trainee_id: DataTypes.INTEGER,
    date: DataTypes.DATE,
    on_time: { type: DataTypes.TIME, defaultValue: null },
    off_time: DataTypes.TIME,
    status: DataTypes.BOOLEAN,
  },
  { tableName: "attendences" }
);
