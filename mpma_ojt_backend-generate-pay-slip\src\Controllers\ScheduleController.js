import { z } from "zod";
import { promisePool } from "../DB/Database.js";
import { getTrainee } from "./TraineeController.js";
import { ErrorWithStatus } from "../ErrorWithStatus.js";
import moment from "moment";
import { Section } from "../models/Section.js";

export const storeSchedules = async (req, res, next) => {
  const schema = z.object({
    traineeId: z.coerce.number(),
    schedules: z
      .array(
        z.object({
          department: z.coerce.number(),
          section: z.coerce.number(),
          start_date: z.string().date(),
          end_date: z.string().date(),
        })
      )
      .min(1, "At least one schedule entry is required"),
  });
  const connection = await promisePool.getConnection();

  try {
    const connection = await promisePool.getConnection();
    const data = schema.parse(req.body);
    connection.beginTransaction();
    try {
      let resultsArray = [];
      for (const schedule of data.schedules) {
        const [results] = await connection.query(
          `
            insert into schedule(
              schedule.department_id,
              schedule.trainee_id,
              schedule.end_date,
              schedule.start_date
              )
            value (?,?,?,?)`,
          [schedule.department, data.traineeId, schedule.end_date, schedule.start_date]
        );
        resultsArray.push(results);
      }
      await connection.commit();
      return res.status(201).json({
        message: "Trainee Schedule Inserted sucessfully",
        queryResults: resultsArray,
      });
    } catch (error) {
      connection.rollback();
      throw error;
    }
  } catch (error) {
    next(error);
  } finally {
    connection.release();
  }
};

export const showSchedules = async (req, res, next) => {
  const schema = z.object({
    traineeId: z.coerce.number(),
  });
  try {
    const data = schema.parse(req.params);
    const trainee = await getTrainee(data.traineeId);
    if (trainee) {
      const schedules = await getScheduleByTraineeId(trainee.id);
      if (schedules) {
        return res.status(200).json(schedules);
      } else {
        throw new ErrorWithStatus(404, "no schedule was found for the trainee ");
      }
    } else {
      throw new ErrorWithStatus(404, "no trainee was found for the id");
    }
  } catch (error) {
    next(error);
  }
};

export const updateSchedules = async (req, res, next) => {
  const schema = z.object({
    traineeId: z.coerce.number(),
    start_date: z.string().date(),
    end_date: z.string().date(),
    period: z.coerce.number(),
    schedules: z.array(
      z.object({
        department: z.coerce.number(),
        start_date: z.string().date(),
        end_date: z.string().date(),
      })
    ),
  });
  try {
    const connection = await promisePool.getConnection();
    try {
      const data = schema.parse({ ...req.body, traineeId: req.params.traineeId });
      const trainee = await getTrainee(data.traineeId);
      if (!trainee) {
        throw new ErrorWithStatus(404, "no trainee was found with the id");
      }
      connection.beginTransaction();
      try {
        const startDate = moment(trainee.start_date).clone();
        const endDate = moment(trainee.end_date).clone();
        if (
          moment(trainee.start_date).format("YYYY-MM-DD") !=
            moment(data.start_date).format("YYYY-MM-DD") ||
          moment(trainee.end_date).format("YYYY-MM-DD") !=
            moment(data.end_date).format("YYYY-MM-DD") ||
          trainee.training_period_id != data.period
        ) {
          //update the journey table
          const [result] = await connection.query(
            "update journey set start_date=?,end_date=?,period_id=? where trainee_id=?;",
            [data.start_date, data.end_date, data.period, trainee.id]
          );
        }
        if (await deleteSchedulesByTraineeId(trainee.id)) {
          //cosnider to change the period
          //consider to change the start_date and end_date
          let resultsArray = [];
          for (const schedule of data.schedules) {
            const [results] = await connection.query(
              `
                  insert into schedule(
                    schedule.department_id,
                    schedule.trainee_id,
                    schedule.end_date,
                    schedule.start_date
                    )
                  value (?,?,?,?)`,
              [schedule.department, data.traineeId, schedule.end_date, schedule.start_date]
            );
            resultsArray.push(results);
          }
          await connection.commit();
          return res.status(201).json({
            message: "Trainee Schedule Inserted sucessfully",
            queryResults: resultsArray,
          });
        } else {
          throw new ErrorWithStatus(500, "failed to delete the existing schedules ");
        }
      } catch (error) {
        connection.rollback();
        throw error;
      }
    } catch (error) {
      next(error);
    } finally {
      connection.release();
    }
  } catch (error) {
    next(error);
  }
};

export const deleteSchedule = async (req, res, next) => {
  try {
    const result = await deleteSchedulesByTraineeId(req.params.traineeId);
    return res.status(200).json(result);
  } catch (error) {
    next(error);
  }
};

/**
 *return the schedule of the trainee
 * @param {number} id id of the trainee
 * @returns {null | object} returns the schedule or null if no schedule was found
 */
const getScheduleByTraineeId = async (id) => {
  const [schedule] = await promisePool.query("SELECT * FROM schedule where trainee_id=?;", [id]);
  if (schedule && schedule.length > 0) {
    schedule.forEach((schedule) => {
      schedule.start_date = moment(schedule.start_date).format("YYYY-MM-DD");
      schedule.end_date = moment(schedule.end_date).format("YYYY-MM-DD");
    });
    return schedule;
  } else {
    return null;
  }
};

/**
 *deletes schedueles of the trainee
 * @param {number} id trainee id
 * @param {*} connection connection for transaction sync queries
 * @returns {Object | null} return the result of the delete query
 */
const deleteSchedulesByTraineeId = async (id, connection) => {
  const query = "delete FROM schedule where trainee_id=?;";
  try {
    if (connection) {
      const results = await connection.query(query, [id]);
      return results;
    } else {
      const results = await promisePool.query(query, [id]);
      return results;
    }
  } catch (error) {

    return null;
  }
};
