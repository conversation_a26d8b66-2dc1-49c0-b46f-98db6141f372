import e from "express";
import {
  activeAccount,
  auth,
  createUser,
  deleteAccount,
  getAllAccessLevels,
  index,
  login,
  suspendAccount,
  updateUser,
  getUserById,
} from "../Controllers/AuthController.js";
import { Auth } from "../Core/AuthWrapper.js";

const router = e.Router();

router.post("/create", await Auth(), createUser);
router.post("/login", login);
router.get("/", auth);
router.put("/user/:id", await Auth(), updateUser);
router.put("/user/:id/suspend", await Auth(), suspendAccount);
router.put("/user/:id/activate", await Auth(), activeAccount);
router.delete("/user/:id", await Auth(), deleteAccount);
router.get("/user", await Auth(), index);
router.get("/user/:id", await Auth(), getUserById);
router.get("/accessLevels", await Auth(), getAllAccessLevels);

export default router;
