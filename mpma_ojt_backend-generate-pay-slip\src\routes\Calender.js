import e from "express";
const router = e.Router();
import {
  create,
  index,
  remove,
  retrieveHolidays,
  retriveWorkingDays,
} from "../Controllers/CalenderController.js";
import { CALENDER_MODIFY, CALENDER_VIEW } from "../Core/Permisions.js";
import { Auth } from "../Core/AuthWrapper.js";

router.get("/:year/:month", retriveWorkingDays);

router.get("/event", await Auth([CALENDER_VIEW, CALENDER_MODIFY]), index);

router.post("/event", await <PERSON>th([CALENDER_MODIFY]), create);

router.delete("/event", await <PERSON>th([CALENDER_MODIFY]), remove);

router.get("holidays/:year/:month", retrieveHolidays);

export default router;
