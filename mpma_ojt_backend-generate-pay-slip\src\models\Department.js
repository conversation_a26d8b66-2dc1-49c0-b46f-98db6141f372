import { sequalize } from "../DB/sequlize.js";
import { DataTypes } from "sequelize";

export const Department = sequalize.define(
  "department",
  {
    id: {
      autoIncrement: true,
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING(255),
      allowNull: false,
    },
    max_count: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: true, // Allow null to avoid setting invalid defaults
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
  },
  { tableName: "department" }
);
