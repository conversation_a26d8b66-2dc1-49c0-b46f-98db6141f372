{"name": "mpma-ojt-backend", "version": "1.0.0", "main": "server.js", "type": "module", "scripts": {"start": "node --env-file=.env server.js", "dev": "node --watch --env-file=src/.env src/server"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"archiver": "^7.0.1", "bson": "^6.10.0", "colors": "^1.4.0", "cors": "^2.8.5", "cron": "^3.3.1", "csv": "^6.3.10", "csv-parse": "^5.5.6", "csv-parser": "^3.0.0", "date-and-time": "^3.6.0", "express": "^4.19.2", "fs": "^0.0.1-security", "jose": "^5.9.6", "moment": "^2.30.1", "mongoose": "^8.9.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.9.8", "mysqldump": "^3.2.0", "nodemon": "^3.1.9", "p-limit": "^6.2.0", "path": "^0.12.7", "sequelize": "^6.37.5", "stream": "^0.0.3", "xlsx": "^0.18.5", "zod": "^3.23.8"}, "description": ""}