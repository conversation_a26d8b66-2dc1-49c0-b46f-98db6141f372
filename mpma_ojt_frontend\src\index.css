:root{
  --primary-color:rgb(9,36,112);
  --secondary-color:#f9df2e;
  --tertiary-color:#fdffff;
}  

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}

a:hover {
  color: #535bf2;
}

body {
  font-size: xx-large;
}



.table-scrollbar {
  max-height: 65vh;
  overflow-y: auto;
}

/* Scrollbar track */
.table-scrollbar::-webkit-scrollbar {
  width: 8px; /* Width of the scrollbar */
  height: 10px;
}

.table-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1; /* Background of the track */
  border-radius: 4px;
}

/* Scrollbar handle */
.table-scrollbar::-webkit-scrollbar-thumb {
  background: #202470; /* Color of the scrollbar */
  border-radius: 4px;
}

/* Scrollbar handle hover */
.table-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #555; /* Color on hover */
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
}



*{
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

/*--------------------------------------------------------------
# General
--------------------------------------------------------------*/
:root {
  scroll-behavior: smooth;
}

body {
  font-family: "Open Sans", sans-serif;
  /* background: #f6f9ff !important; */
  background: #f1f6ff !important;
  color: #444444;
}


h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: "Nunito", sans-serif;
}



/* Tabel */

.pagetitle {
margin-bottom: 10px;
}

.pagetitle h1 {
font-size: 24px;
margin-bottom: 0;
font-weight: 600;
color: #012970;
}


.button-container {
display: flex;
/* Enables Flexbox layout */
justify-content: flex-end;
/* Aligns buttons to the right */
margin-top: 10px;
/* Space above the buttons */
}

.buttonPrimary {
font-weight:600;
padding: 5px 10px;
/* Padding around the button */
width: 250px;
/* Width of the button */
background-color:rgb(9, 36, 112);
/* Background color */
color: white;
/* Text color */
border: none;
/* No border */
border-radius: 5px;
/* Rounded corners */
cursor: pointer;
/* Pointer cursor on hover */
margin-left: 10px;
/* Space between buttons */
}

.buttonSecondary{

background-color: #f9df2e;
;
font-weight:600;
padding: 5px 10px;
/* Padding around the button */
width: 250px;
/* Width of the button */
color: white;
/* Text color */
border: none;
/* No border */
border-radius: 5px;
/* Rounded corners */
cursor: pointer;
/* Pointer cursor on hover */
margin-left: 10px;
/* Space between buttons */

}

.buttons:hover {
background-color: #006f7d;
/* Darker background on hover */
}


.buttons:hover {
background-color: #006f7d;
}



@media (max-width: 1199px) {
#main {
  padding: 20px;
}
}

@media (min-width: 1200px) {

#main,
#footer {
  margin-left: 300px;
}
}

@media (min-width: 1200px) {

.toggle-sidebar #main,
.toggle-sidebar #footer {
  margin-left: 0;
}

.toggle-sidebar .sidebar {
  left: -300px;
}
}

/* Info mains */
.dashboard .info-card {
padding-bottom: 10px;
}




/* Breadcrumbs */
.breadcrumb {
font-size: 14px;
font-family: "Nunito", sans-serif;
color: #051736;
font-weight: 600;
}

.breadcrumb a {
color: #051736;
transition: 0.3s;
}

.breadcrumb a:hover {
color: #051736;
;
}

.breadcrumb .breadcrumb-item::before {
color: #051736;

}

.breadcrumb .active {
color: #051736;
font-weight: 600;

}

.image{
  padding: 5px;
  width: 50px;
}
th{
text-align: center;
}

/* table {
border-radius:4px;
width:70%;
margin:100px auto;
background-color: rgb(250, 250, 250);
border-collapse: collapse; 
} */


/* tr:first-child:hover td {
border-top: none;
}
tr:last-child:hover td {
border-bottom: none;
} */

#icon{
padding: 5px;
width: 90px;



}
.active .icon32.icon-star-on, .icon32.icon-star-on, .icon32.icon-star-on:hover {
background-position : -448px -96px;
}
.bi.bi-info-circle-fill, .bi-info-circle-fill .bi {

width: 16px;
height: 16px;
display: block;

}
.bi.bi-info-circle-fill:hover:after {
content: "See Profile";
display: block;
position: relative;
top: -16px;
right: -16px;
width: 100px;
background: rgb(199, 224, 233);
}

.active .icon32.icon-star-on, .icon32.icon-star-on, .icon32.icon-star-on:hover {
background-position : -448px -96px;
}
.bi.bi-person-fill-add, .bi-person-fill-add .bi {

width: 16px;
height: 16px;
display: block;

}
.bi.bi-person-fill-add:hover:after {
content: "Add Selected";
display: block;
position: relative;
top: -16px;
right: -16px;
width: 100px;
background-color :  rgb(199, 224, 233);
}

