import express from "express";
import { PAYMENT_VIEW, PAYMENTS_MODIFY } from "../Core/Permisions.js";
import {
  showBankDetails,
  storeTraineeBankDetails,
  updateBankDetails,
  generateSlip, 
  getPaymentSummary, 
  downloadPayamentDetails,
  removeFromPaymentList,
  addToPaymentList,
  updatePaymentPerDay
} from "../Controllers/PaymentController.js";
import { Auth } from "../Core/AuthWrapper.js";

const router = express.Router();

router.post("/", await Auth([PAYMENTS_MODIFY]), storeTraineeBankDetails);

router.get(
  "/:traineeId",
  await <PERSON>th([PAYMENTS_MODIFY, PAYMENT_VIEW, "trainee:self"]),
  showBankDetails
);

router.put("/:traineeId", await <PERSON>th([PAYMENTS_MODIFY]), updateBankDetails);

router.post("/generatePaySlip", await <PERSON>th([PAYMENT_VIEW,PAYMENTS_MODIFY]), generateSlip);

router.get("/generatePaySlip/summary", await Auth([PAYMENT_VIEW,PAYMENTS_MODIFY]), getPaymentSummary);

router.post("/downloadPaymentDetails", await Auth([PAYMENT_VIEW,PAYMENTS_MODIFY]), downloadPayamentDetails);

router.post("/removeFromPaymentList", await Auth([PAYMENT_VIEW,PAYMENTS_MODIFY]), removeFromPaymentList);

router.post("/addToPaymentList", await Auth([PAYMENT_VIEW,PAYMENTS_MODIFY]), addToPaymentList);

router.post("/updatePaymentPerDay", await Auth([PAYMENTS_MODIFY]), updatePaymentPerDay);

//router.get("/getDateSummary", getDateSummary);


export default router;