import { DataTypes } from "sequelize";
import { sequalize } from "../DB/sequlize.js";

export const Trainee = sequalize.define("trainee", {
  id: {
    autoIncrement: true,
    type: DataTypes.INTEGER,
    allowNull: false,
    primaryKey: true,
  },
  name: {
    type: DataTypes.STRING(500),
    allowNull: false,
  },
  ATT_NO: {
    type: DataTypes.INTEGER,
    allowNull: false,
  },
  REG_NO: {
    type: DataTypes.STRING(25),
    allowNull: false,
    unique: "REG_NO",
  },
  NIC_NO: {
    type: DataTypes.STRING(20),
    allowNull: false,
  },
  contact_no: {
    type: DataTypes.STRING(10),
    allowNull: false,
  },
  status: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
  },
  institute_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: "institute",
      key: "id",
    },
  },
  training_program_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: "training_program",
      key: "id",
    },
  },
});

export const Schedule = sequalize.define(
  "schedule",
  {
    id: {
      autoIncrement: true,
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true,
    },
    trainee_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: "trainee",
        key: "id",
      },
    },
    department_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: "department",
        key: "id",
      },
    },
    start_date: {
      type: DataTypes.DATEONLY,
      allowNull: false,
    },
    end_date: {
      type: DataTypes.DATEONLY,
      allowNull: false,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: true, // Allow null to avoid setting invalid defaults
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
  },
  {
    tableName: "schedule",
  }
);
