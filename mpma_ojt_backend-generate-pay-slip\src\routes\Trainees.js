import express from "express";
import {
  checkEligibility,
  checkRegNoViolation,
  deleteInterview,
  filterData,
  generateIndexNos,
  getAvailableTrainingPeriods,
  getInterview,
  getTrainingPrograms,
  index,
  show,
  store,
  update,
  getTraineeDetails,
} from "../Controllers/TraineeController.js";
import {
  deleteSchedule,
  showSchedules,
  storeSchedules,
  updateSchedules,
} from "../Controllers/ScheduleController.js";
import { getHolidaysArray } from "../Controllers/CalenderController.js";
import { getAttendenceOfTrainee } from "../Controllers/AttendenceController.js";
import { Auth } from "../Core/AuthWrapper.js";
import {
  PAYMENT_VIEW,
  PAYMENTS_MODIFY,
  SCHEDULES_MODIFY,
  SCHEDULES_VIEW,
  TRAINEES_MODIFY,
  TRAINEES_VIEW,
} from "../Core/Permisions.js";
import ro from "date-and-time/locale/ro";
import { deleteRecord } from "../Controllers/InterviewController.js";

const router = express.Router();

router.post("/", await Auth([TRAINEES_MODIFY]), store);
router.post("/schedule", await Auth([SCHEDULES_MODIFY]), storeSchedules);
router.get(
  "/:traineeId/schedule",
  await Auth([SCHEDULES_VIEW, SCHEDULES_MODIFY, "trainee:self"]),
  showSchedules
);
router.get("/:traineeId/interview", getInterview);
router.delete("/:traineeId/interview", deleteInterview);
router.put(
  "/:traineeId/schedule",
  await Auth([SCHEDULES_MODIFY]),
  updateSchedules
);
router.get("/", await Auth([TRAINEES_MODIFY, TRAINEES_VIEW]), index);
router.get("/periods", getAvailableTrainingPeriods);
router.get("/training_programmes", getTrainingPrograms);
router.get(
  "/filter_data",
  await Auth([TRAINEES_MODIFY, TRAINEES_VIEW]),
  filterData
);

router.get("/eligibility/:NIC_NO", checkEligibility);
router.get("/regNoViolation", checkRegNoViolation);
router.get(
  "/genearateIndexes",
  await Auth([TRAINEES_MODIFY]),
  generateIndexNos
);
router.get(
  "/:traineeId",
  await Auth([TRAINEES_VIEW, TRAINEES_MODIFY, "trainee:self"]),
  show
);
router.get(
  "/:traineeId/attendence",
  await Auth([TRAINEES_MODIFY, TRAINEES_VIEW, "trainee:self"]),
  getAttendenceOfTrainee
);
router.put("/:id", await Auth([TRAINEES_MODIFY]), update);

router.delete(
  "/:traineeId/schedule",
  await Auth([SCHEDULES_MODIFY]),
  deleteSchedule
);

router.get("/traineeDetails", await Auth([TRAINEES_MODIFY]), getTraineeDetails);

router.delete("/interview/:NIC", await Auth([TRAINEES_MODIFY]), deleteRecord);

export default router;
